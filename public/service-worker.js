// Service worker for push notifications
self.addEventListener('install', (event) => {
    console.log('install', event)
    self.skipWaiting();
});

self.addEventListener('activate', (event) => {
    console.log('activate', event)
    return self.clients.claim();
});

// Handle push events from the server
self.addEventListener('push', (event) => {
    console.log('push', event)
    if (!event.data) return;
    try {
        const data = event.data.json();
        const options = {
            body: data.message || 'New notification',
            icon: data.icon || '/solaris.png',
            data: {
                url: data.url ?? '/'
            }
        };

        event.waitUntil(
            self.registration.showNotification(data.title || 'Solaris Notification', options)
        );
    } catch (error) {
        console.error('Error showing push notification:', error);
    }
});

// Handle notification click
self.addEventListener('notificationclick', (event) => {
    console.log('notificationclick', event)
    event.notification.close();

    const newUrl = event.notification.data.url ?? '/';

    event.waitUntil(
        clients.matchAll({ type: 'window' }).then((clientList) => {
            // If a window client is already open, focus it and navigate to the URL
            for (const client of clientList) {
                if ('focus' in client) {
                    // Focus the window first
                    return client.focus().then((focusedClient) => {
                        // Then navigate to the new URL
                        return focusedClient.navigate(newUrl);
                    });
                }
            }
            // Otherwise open a new window with the URL
            if (clients.openWindow) {
                return clients.openWindow(newUrl);
            }
        })
    );
});
