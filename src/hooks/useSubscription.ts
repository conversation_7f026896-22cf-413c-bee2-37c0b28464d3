import { useState, useRef, useCallback, useEffect } from 'react';
import { TSolarisProps, TTick, TRange, TPosition } from '../types';
import { ChartData } from '../storage/chartData';

export function useSubscription(
  props: TSolarisProps,
  chartDataList: ChartData[],
  timeRange: TRange,
  timeUnit: number
) {
  const [tick, setTick] = useState<TTick | null>(null);
  const positionsRef = useRef<TPosition[]>([]);
  const socket = useRef<WebSocket>(null);

  // Function to check if we're at the "live edge" (near current time)
  const isAtLiveEdge = useCallback((timeRangeMax: number) => {
    return Date.now() - timeRangeMax < timeUnit * 5; // Within 5 candles of current time
  }, [timeUnit]);

  // Add a function to check if zoom would exceed the 2000 bar limit
  const wouldExceedZoomLimit = useCallback((newTimeRange: TRange) => {
    const barsInView = (newTimeRange.max - newTimeRange.min) / timeUnit;
    return barsInView > 2000;
  }, [timeUnit]);

  // Function to manage subscription
  const manageSubscription = useCallback(() => {
    if (!chartDataList.length || props.isChangingSymbol) return;

    const dataTimeRange = chartDataList[0].getTimeRange();
    if (!dataTimeRange) return;

    // Check if we need to subscribe based on timeRange
    const shouldBeSubscribed = isAtLiveEdge(timeRange.max);
    const isCurrentlySubscribed = !!socket.current;

    // Only subscribe if:
    // 1. We're not already subscribed
    // 2. We're at the live edge
    // 3. Our latest data is reasonably current (within 2 timeframe units of now)
    if (shouldBeSubscribed && !isCurrentlySubscribed && dataTimeRange.max + timeUnit * 2 >= Date.now()) {
      try {
        console.log('Subscribing to live data, latest data time:', new Date(dataTimeRange.max).toISOString());

        socket.current = props.loader.subscribeData(props.symbol, props.timeframe, (payload) => {
          // Only process updates if we're not in the middle of changing symbols
          if (props.isChangingSymbol) return;

          switch (payload.topic) {
            case `chartdata:${props.symbol}:${props.timeframe}`:
              if (payload.updates && Array.isArray(payload.updates)) {
                console.log(`Received ${payload.updates.length} updates for ${props.symbol}:${props.timeframe}`);
                for (const streamData of payload.updates) {
                  chartDataList.forEach(chartData => chartData.updateData(streamData));
                }
              }
              break;
            case `tick:${props.symbol}:${props.timeframe}`:
              if (payload.tick) {
                // Update tick if:
                // 1. We don't have a tick yet
                // 2. The new tick has a newer timestamp
                // 3. The timestamp is the same but the price has changed
                if (!tick ||
                  payload.tick.time > tick.time ||
                  (payload.tick.time === tick.time && payload.tick.price !== tick.price)) {
                  setTick(payload.tick);
                }
              }
              break;
            case 'position':
              if (payload.positions && Array.isArray(payload.positions)) {
                positionsRef.current = payload.positions;
              }
              break;
            default:
              break;
          }
        });
        console.log('Successfully subscribed to live data');
      } catch (error) {
        console.error("Error subscribing to data:", error);
      }
    } else if (!shouldBeSubscribed && isCurrentlySubscribed) {
      // We're no longer at the live edge but still have an active subscription
      // We'll keep it active as per the requirement to maintain subscription once established
      console.log('Maintaining subscription despite not being at live edge');
    }
  }, [props.symbol, props.timeframe, props.loader, chartDataList, props.isChangingSymbol, timeUnit, timeRange, tick, isAtLiveEdge]);

  // Function to clean up subscription
  const cleanupSubscription = useCallback(() => {
    if (socket.current) {
      console.log('Closing WebSocket connection');
      socket.current.close();
      socket.current = null;
      setTick(null);
    }
  }, []);

  // Clean up subscription when component unmounts
  useEffect(() => {
    return () => {
      cleanupSubscription();
    };
  }, [cleanupSubscription]);

  return {
    tick,
    setTick,
    socket,
    isAtLiveEdge,
    wouldExceedZoomLimit,
    manageSubscription,
    cleanupSubscription,
    positionsRef
  };
}