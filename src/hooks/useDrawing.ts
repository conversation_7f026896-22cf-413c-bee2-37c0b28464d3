import { useState, useCallback, useContext, useRef, useMemo, useEffect, use } from "react";
import Kon<PERSON> from "konva";
import { AppContext } from "../contexts/app.context";
import { PaneContext } from "../contexts/pane.context";
import { useScale } from "./useScale";
import { createDrawingConfig, updateDrawingConfig, deleteDrawingConfig } from "../common/api";
import { TDrawingConfig, TDataPoint, TCoord, ETool } from "../types";
import { ALERT_COLOR, DEFAULT_DRAWING_COLOR } from "../common/constants";

export function useDrawing({
    groupRef,
    config,
    anchorCount = 2,
    onDrawEnd,
    onDragEnd,
}: {
    groupRef: React.RefObject<Konva.Group | null>;
    config: TDrawingConfig;
    anchorCount?: number;
    onDrawEnd?: () => void | Promise<void>;
    onDragEnd?: () => void | Promise<void>;
}) {
    const [dataPoints, setDataPoints] = useState<TDataPoint[]>(config.dataPoints || []);
    const [isDrawing, setIsDrawing] = useState(config.dataPoints.length < anchorCount);
    const [isSelected, setIsSelected] = useState(false);
    const { crosshairsCoord, alertConfigList, setActiveTool } = useContext(AppContext);
    const { setSelectedDrawingConfig, selectedDrawingConfig } = useContext(PaneContext);
    const dragStartCrosshairsCoord = useRef<TCoord | null>(null);
    const dragStartGroupCoord = useRef<TCoord | null>(null);
    const [isDraging, setIsDraging] = useState(false);

    const { x2t, y2v, t2x, v2y } = useScale();

    // Alert and styling
    const hasAlert = useCallback(
        () =>
            config.id !== undefined &&
            alertConfigList.some(
                (alert) => alert.arg2?.type === "drawing" && alert.arg2.refId === config.id?.toString()
            ),
        [config.id, alertConfigList]
    );

    const drawingColor = hasAlert() ? ALERT_COLOR : DEFAULT_DRAWING_COLOR;
    const lineDash = hasAlert() ? [5, 5] : undefined;

    // Coordinate transformation
    const toRelativeCoord = (coord: TCoord) => {
        const groupPos = groupRef.current?.position() || { x: 0, y: 0 };
        return { x: coord.x - groupPos.x, y: coord.y - groupPos.y };
    }
    const toRelativeX = (x: number) => {
        if (!groupRef.current) return x;
        const groupPos = groupRef.current.position()
        return x - groupPos.x;
    }

    const toRelativeY = (y: number) => {
        if (!groupRef.current) return y;
        const groupPos = groupRef.current.position()
        return y - groupPos.y;
    }

    const toAbsoluteY = (y: number) => {
        if (!groupRef.current) return y;
        const groupPos = groupRef.current.position()
        return y + groupPos.y;
    }

    const toAbsoluteCoord = (coord: TCoord) => {
        const groupPos = groupRef.current?.position() || { x: 0, y: 0 };
        return { x: coord.x + groupPos.x, y: coord.y + groupPos.y };
    }

    const previewCoord = !crosshairsCoord.current || !isDrawing ? null : toRelativeCoord(crosshairsCoord.current);

    const anchorCoords = useMemo(() => {
        return dataPoints.map(dp => {
            return toRelativeCoord({ x: t2x(dp.time), y: v2y(dp.value!) });
        })
    }, [dataPoints, toRelativeCoord, t2x, v2y])

    const handleDrawEnd = () => {
        blur()
        createDrawingConfig({
            timeframe: config.timeframe,
            symbol: config.symbol,
            chart: config.chart,
            type: config.type,
            dataPoints
        }).finally(async () => {
            await onDrawEnd?.()
            setActiveTool(ETool.None)
            const stage = groupRef.current!.getStage()!
            stage.off('click', addAnchor)
        })
    }

    const addAnchor = () => {
        if (!crosshairsCoord.current || dataPoints.length >= anchorCount) return;
        setDataPoints(prev => [...prev, { time: x2t(crosshairsCoord.current!.x), value: y2v(crosshairsCoord.current!.y) }]);
    }

    const showAnchors = () => {
        const anchors = groupRef.current?.find('.anchor')
        anchors?.forEach(anchor => {
            anchor.show()
        })
    }
    const hideAnchors = () => {
        const anchors = groupRef.current?.find('.anchor')
        anchors?.forEach(anchor => {
            anchor.hide()
        })
    }

    const blur = () => {
        if (!groupRef.current || !groupRef.current.getStage()) return;
        groupRef.current.getStage()!.container().style.cursor = "default";
    }

    const focus = () => {
        if (!groupRef.current || !groupRef.current.getStage()) return;
        groupRef.current.getStage()!.container().style.cursor = "pointer";
    }

    const select = () => {
        setSelectedDrawingConfig(config)
        setIsSelected(true)
        showAnchors()
    }

    const deselect = () => {
        setSelectedDrawingConfig(null)
        setIsSelected(false)
        hideAnchors()
    }

    // handle drawing start
    useEffect(() => {
        if (!groupRef.current) return
        const stage = groupRef.current.getStage()!
        if (dataPoints.length === anchorCount) return
        setIsDrawing(true)
        stage.on('click', addAnchor)
        return () => {
            stage.off('click', addAnchor)
        }

    }, [groupRef.current, addAnchor])

    useEffect(() => {
        if (!groupRef.current) return
        const stage = groupRef.current.getStage()!

        const emptyClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
            if (e.target === stage && e.evt.button === 0) {
                deselect()
            }
        }
        stage.on('click', emptyClick)

        return () => {
            stage.off('click', emptyClick)
        }
    }, [groupRef])

    // handle draw end
    useEffect(() => {
        if (!groupRef.current) return
        if (dataPoints.length === anchorCount && isDrawing) {
            setIsDrawing(false)
            handleDrawEnd()
        }
    }, [groupRef, isDrawing, dataPoints])


    useEffect(() => {
        hideAnchors()
    }, [])

    useEffect(() => {
        const onKeyDown = async (e: KeyboardEvent) => {
            if (e.key === "Backspace" && isSelected) {
                if (!config.id) return;
                // Mark this key press as handled
                e.stopPropagation();

                // Delete the drawing
                await deleteDrawingConfig(config.id);
                deselect();
                blur();
                groupRef.current?.destroy();
                document.removeEventListener('keydown', onKeyDown);
            }
        };

        document.addEventListener('keydown', onKeyDown);
        return () => {
            document.removeEventListener('keydown', onKeyDown);
        };
    }, [isSelected])

    useEffect(() => {
        if (selectedDrawingConfig && config.id !== selectedDrawingConfig?.id) {
            setIsSelected(false)
            hideAnchors()
        }
    }, [selectedDrawingConfig])


    const handleDragMove = useCallback((e: Konva.KonvaEventObject<MouseEvent>) => {
        if (!groupRef.current) return;
        // Update data points based on current anchor positions
        const anchors = groupRef.current.find('.anchor')
        const groupCoord = groupRef.current.position()

        const newDataPoints = anchors.map(dot => {
            const dotCoord = dot.position()
            return {
                time: x2t(dotCoord.x + groupCoord.x),
                value: y2v(dotCoord.y + groupCoord.y)
            }
        })
        setDataPoints(newDataPoints)
    }, [groupRef, x2t, y2v])

    const handleDragStart = (e: Konva.KonvaEventObject<MouseEvent>) => {
        dragStartCrosshairsCoord.current = crosshairsCoord.current;
        dragStartGroupCoord.current = groupRef.current!.position()
        focus()
        setIsDraging(true)
    }

    const handleDragEnd = () => {
        dragStartCrosshairsCoord.current = null;
        dragStartGroupCoord.current = null;
        if (config.id) {
            updateDrawingConfig(config.id, dataPoints)
            onDragEnd?.()
        }
        blur()
        setIsDraging(false)
    }

    const handleClick = () => {
        if (config.id) {
            select()
        }
    }

    const handleMouseOver = () => {
        if (isDrawing) return
        showAnchors()
        focus()
    }

    const handleMouseOut = () => {
        blur()
        if (isDrawing || isSelected) return
        hideAnchors()
    }


    const groupDragBoundFunc = function (this: Konva.Node, pos: TCoord) {
        if (!crosshairsCoord.current || !dragStartCrosshairsCoord.current) return pos;
        const deltaX = dragStartCrosshairsCoord.current.x - crosshairsCoord.current.x
        return {
            y: pos.y,
            x: dragStartGroupCoord.current!.x - deltaX
        }
    }

    const anchorDragBoundFunc = function (this: Konva.Node, pos: TCoord) {
        if (!crosshairsCoord.current) return pos;
        return {
            y: pos.y,
            x: crosshairsCoord.current.x ?? pos.x
        }
    }

    return {
        previewCoord,
        anchorCoords,
        drawingColor,
        lineDash,
        handleDragMove,
        handleDragStart,
        handleDragEnd,
        handleClick,
        handleMouseOver,
        handleMouseOut,
        groupDragBoundFunc,
        anchorDragBoundFunc,
        toRelativeCoord,
        toAbsoluteCoord,
        toRelativeX,
        toRelativeY,
        toAbsoluteY,
        dataPoints,
        isDrawing,
        isDraging
    }
}