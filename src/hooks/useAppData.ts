import { useState } from 'react';
import { ChartData } from '../storage/chartData';
import { fetchAlertConfigs } from '../common/api';
import { TSolarisProps, TAlertConfig, TRange } from '../types';

export function useAppData(
  props: TSolarisProps,
  timeRange: TRange,
  timeUnit: number
) {
  const [chartDataList, setChartDataList] = useState<ChartData[]>([]);
  const [loadingChartData, setLoadingChartData] = useState(false);
  const [alertConfigList, setAlertConfigList] = useState<TAlertConfig[]>([]);

  const loadInitialData = async () => {
    // Clear existing data when symbol or timeframe changes
    setChartDataList([]);

    // Don't load data if we're in the middle of changing symbols
    if (props.isChangingSymbol) {
      return;
    }

    setLoadingChartData(true);

    console.log('Loading initial data for', props.symbol, props.timeframe,
      'timeRange:', new Date(timeRange.min).toISOString(), 'to', new Date(timeRange.max).toISOString());

    try {
      // Use Promise.all to load chart data and alert configs in parallel
      const [chartData, alertConfigs] = await Promise.all([
        props.loader.getChartDataList({
          symbol: props.symbol,
          timeframe: props.timeframe,
          startTime: timeRange.min,
          endTime: Math.min(timeRange.max, Date.now()), // Don't request future data
          limit: timeRange ? undefined : 500
        }),
        fetchAlertConfigs()
      ]);

      console.log(`Loaded ${chartData.length} charts with data`);
      if (chartData.length > 0 && chartData[0].plots.length > 0) {
        // Log the time range of the loaded data
        const firstChart = chartData[0];
        const firstPlot = firstChart.plots[0];
        if (firstPlot.data && Array.isArray(firstPlot.data) && firstPlot.data.length > 0) {
          const firstTime = firstPlot.data[0].time;
          const lastTime = firstPlot.data[firstPlot.data.length - 1].time;
          console.log('Data range:', new Date(firstTime).toISOString(), 'to', new Date(lastTime).toISOString());
        }
      }

      setChartDataList(chartData.map(d => new ChartData(d)));
      setAlertConfigList(alertConfigs);
    } catch (error) {
      console.error("Error loading data:", error);
    } finally {
      setLoadingChartData(false);
    }
  };

  const loadMoreData = async (currentTimeRange: TRange) => {
    if (loadingChartData || !chartDataList.length || props.isChangingSymbol) return;

    const dataTimeRange = chartDataList[0].getTimeRange();
    if (!dataTimeRange) return;

    // Load historical data if needed
    if (currentTimeRange.min < dataTimeRange.min - timeUnit * 2) {
      try {
        const data = await props.loader.getChartDataList({
          symbol: props.symbol,
          timeframe: props.timeframe,
          endTime: dataTimeRange.min - 1,
          limit: 100
        });

        for (const chartData of data) {
          chartDataList.find(cd => cd.name === chartData.name)?.prependData(chartData);
        }
      } catch (error) {
        console.error("Error loading historical data:", error);
      }
    }

    // Load future data if needed
    const isAtLiveEdge = Date.now() - currentTimeRange.max < timeUnit * 5;
    if (
      dataTimeRange &&
      currentTimeRange.max > dataTimeRange.max + timeUnit * 2 &&
      Date.now() - dataTimeRange.max > timeUnit * 1.5 &&
      !isAtLiveEdge
    ) {
      console.log('Loading future data:', new Date(dataTimeRange.max).toISOString(), 'to', new Date(currentTimeRange.max).toISOString());

      try {
        const data = await props.loader.getChartDataList({
          symbol: props.symbol,
          timeframe: props.timeframe,
          startTime: dataTimeRange.max + 1,
          limit: 100
        });

        // Only append if we actually got new data
        if (data.length > 0 && data[0].plots.length > 0) {
          for (const chartData of data) {
            chartDataList.find(cd => cd.name === chartData.name)?.appendData(chartData);
          }
        } else {
          console.log('No new future data available');
        }
      } catch (error) {
        console.error("Error loading future data:", error);
      }
    }
  };

  return {
    chartDataList,
    loadingChartData,
    alertConfigList,
    setAlertConfigList,
    loadInitialData,
    loadMoreData
  };
}