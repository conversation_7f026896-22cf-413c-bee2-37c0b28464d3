import { useState, useEffect, RefObject } from 'react';

export function useLayout(containerRef: RefObject<HTMLDivElement | null>) {
  const [width, setWidth] = useState(0);
  const [height, setHeight] = useState(0);

  useEffect(() => {
    if (!containerRef.current) return;

    const obs = new ResizeObserver(() => {
      setWidth(containerRef.current!.clientWidth);
      setHeight(containerRef.current!.clientHeight);
    });

    obs.observe(containerRef.current);

    return () => {
      if (containerRef.current) {
        obs.unobserve(containerRef.current);
      }
      obs.disconnect();
    };
  }, [containerRef]);

  const getChartSizes = (chartCount: number, availHeight: number) => {
    const totalFlex = 4 + (chartCount - 1);
    const chartSizes = [];

    for (let i = 0; i < chartCount; i++) {
      const flex = i === 0 ? 4 : 1;
      const chartHeight = (flex / totalFlex) * availHeight;
      const top = i === 0 ? 0 : ((4 + i - 1) / totalFlex) * availHeight;

      chartSizes.push({
        height: chartHeight,
        top
      });
    }

    return chartSizes;
  };

  return { width, height, getChartSizes };
}
