import { useContext, useEffect, useRef, useState } from 'react';
import { AppContext } from '../contexts/app.context';
import { useScale } from './useScale';
import Konva from 'konva';
import { MAIN_CHART_NAME } from '../common/constants';
import { TContextMenuState } from '../types';

export function useContextMenu() {
  const [contextMenuState, setContextMenuState] = useState<TContextMenuState | null>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const { activeChart, crosshairsCoord } = useContext(AppContext);
  const { y2v } = useScale()

  // Handle context menu open - only for main chart
  const onContextMenu = (e: Konva.KonvaEventObject<PointerEvent>) => {
    // Prevent default context menu
    e.evt.preventDefault();
    e.cancelBubble = true

    // Only open context menu on main chart
    if (activeChart !== MAIN_CHART_NAME || !crosshairsCoord) return;
    if (!crosshairsCoord.current) return;

    setContextMenuState({
      x: crosshairsCoord.current.x,
      y: crosshairsCoord.current.y,
      price: y2v(crosshairsCoord.current.y)
    });
  };

  // Handle context menu close
  const closeContextMenu = () => {
    setContextMenuState(null);
  };

  // Set up document-wide event handlers for the context menu
  useEffect(() => {
    if (!contextMenuState) return;

    // Function to handle clicks anywhere in the document
    const handleDocumentClick = (e: MouseEvent) => {
      // Check if the click is outside the menu
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        closeContextMenu();
      }
    };

    // Function to handle key presses
    const handleKeyDown = (e: KeyboardEvent) => {
      // Close the menu when Escape key is pressed
      if (e.key === 'Escape') {
        closeContextMenu();
      }
    };

    // Add the event handlers to the document
    document.addEventListener('click', handleDocumentClick);
    document.addEventListener('keydown', handleKeyDown);

    // Clean up
    return () => {
      document.removeEventListener('click', handleDocumentClick);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [contextMenuState]);

  return {
    contextMenuState,
    menuRef,
    onContextMenu,
    closeContextMenu
  };
}
