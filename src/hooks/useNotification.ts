import React, { useState, useCallback, useEffect, useContext } from 'react';
import { TNotification } from '../types';
import { savePushSubscription } from '../common/api';
import { isApp } from '../common/util';

const PUBLIC_VAPID_KEY = 'BPb3dU4XzYkcjX7lOyHy4dZ7bRqvuqQGhXXVAdQXvQ933XzyBjS4upA36BSqW7NhTrWLbEITVqUJ2nZyB8odZsw'; // Replace with your VAPID key
const PUSH_SUBSCRIPTION_KEY = 'push_subscription';

// Notification context to manage notifications globally
export const NotificationContext = React.createContext<{
  addNotification: (notification: Omit<TNotification, 'id'>) => void;
  removeNotification: (id: string) => void;
  requestNotificationPermission: () => Promise<boolean>;
  hasNotificationPermission: () => boolean;
  subscribeToPushNotifications?: () => Promise<PushSubscription | null>;
  unsubscribeFromPushNotifications?: () => Promise<boolean>;
  isPushSubscribed?: boolean;
}>({
  addNotification: () => { },
  removeNotification: () => { },
  requestNotificationPermission: async () => false,
  hasNotificationPermission: () => false,
});

export const useNotificationProvider = () => {
  const [notifications, setNotifications] = useState<TNotification[]>([]);
  const [_, setNotificationPermission] = useState<NotificationPermission | null>(null);
  const [serviceWorkerRegistration, setServiceWorkerRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [pushSubscription, setPushSubscription] = useState<PushSubscription | null>(null);

  // Check if the browser supports notifications
  const isNotificationSupported = useCallback(() => {
    return 'Notification' in window;
  }, []);

  // Check if the page is currently visible (used for debugging)
  // const isPageVisible = useCallback(() => {
  //   return document.visibilityState === 'visible';
  // }, []);

  // Check if we have permission to show notifications
  const hasNotificationPermission = useCallback(() => {
    if (!isNotificationSupported()) return false;
    return Notification.permission === 'granted';
  }, [isNotificationSupported]);

  // Request permission to show notifications
  const requestNotificationPermission = useCallback(async () => {
    if (!isNotificationSupported()) return false;

    try {
      const permission = await Notification.requestPermission();
      setNotificationPermission(permission);
      return permission === 'granted';
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  }, [isNotificationSupported]);

  // Initialize notification permission state
  useEffect(() => {
    if (isNotificationSupported()) {
      setNotificationPermission(Notification.permission);
    }
  }, [isNotificationSupported]);

  // Show a system notification
  const showSystemNotification = useCallback((notification: TNotification) => {
    if (!hasNotificationPermission()) return;

    try {
      const systemNotification = new window.Notification(
        notification.title || 'Solaris Notification',
        {
          body: notification.message,
          icon: notification.icon || '/solaris.png',
          tag: notification.id,
          data: {
            url: notification.url || '/' // Include URL for navigation
          }
        }
      );

      // Auto close the notification after the duration
      if (notification.duration) {
        setTimeout(() => {
          systemNotification.close();
        }, notification.duration);
      }

      // Handle notification click
      systemNotification.onclick = () => {
        // Focus the window
        window.focus();

        // Navigate to the URL if provided
        if (notification.url && window.location.pathname !== notification.url) {
          window.history.pushState({}, '', notification.url);
          // Dispatch a popstate event to trigger URL parameter handling
          window.dispatchEvent(new PopStateEvent('popstate'));
        }

        // Close the notification
        systemNotification.close();
      };
    } catch (error) {
      console.error('Error showing system notification:', error);
    }
  }, [hasNotificationPermission]);

  const addNotification = useCallback((notification: Omit<TNotification, 'id'>) => {
    const id = Math.random().toString(36).substring(2, 9);
    const fullNotification = { ...notification, id };

    // Add to internal notifications state
    setNotifications((prev) => [...prev, fullNotification]);

    // Always show system notification if we have permission (regardless of useSystemNotification flag)
    if (hasNotificationPermission()) {
      showSystemNotification(fullNotification);
    }
  }, [hasNotificationPermission, showSystemNotification]);

  const removeNotification = useCallback((id: string) => {
    setNotifications((prev) => prev.filter((notification) => notification.id !== id));
  }, []);

  // Check if service worker is supported
  const isServiceWorkerSupported = useCallback(() => {
    return 'serviceWorker' in navigator;
  }, []);

  // Register service worker
  const registerServiceWorker = useCallback(async () => {
    if (!isServiceWorkerSupported()) return null;

    try {
      const registration = await navigator.serviceWorker.register('/service-worker.js');
      console.log('Service Worker registered with scope:', registration.scope);
      setServiceWorkerRegistration(registration);
      return registration;
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      return null;
    }
  }, [isServiceWorkerSupported]);

  // Subscribe to push notifications
  const subscribeToPushNotifications = useCallback(async () => {
    if (!serviceWorkerRegistration) return null;

    try {
      // Check for existing subscription
      let subscription = await serviceWorkerRegistration.pushManager.getSubscription();
      if (!subscription) {
        // Create new subscription
        subscription = await serviceWorkerRegistration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: urlBase64ToUint8Array(PUBLIC_VAPID_KEY)
        });

        // Save subscription to localStorage
        localStorage.setItem(PUSH_SUBSCRIPTION_KEY, JSON.stringify(subscription));

        // Here you would typically send the subscription to your server
      }
      if (isApp()) {
        await savePushSubscription(subscription);
      }


      setPushSubscription(subscription);
      return subscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      return null;
    }
  }, [serviceWorkerRegistration]);

  // Unsubscribe from push notifications
  const unsubscribeFromPushNotifications = useCallback(async () => {
    if (!pushSubscription) return false;

    try {
      const success = await pushSubscription.unsubscribe();
      if (success) {
        setPushSubscription(null);
        localStorage.removeItem(PUSH_SUBSCRIPTION_KEY);
        // Here you would typically inform your server
        // await removeSubscriptionFromServer(pushSubscription);
      }
      return success;
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      return false;
    }
  }, [pushSubscription]);

  // Initialize service worker and push subscription
  useEffect(() => {
    if (isServiceWorkerSupported()) {
      registerServiceWorker().then(registration => {
        if (registration && hasNotificationPermission()) {
          subscribeToPushNotifications();
        }
      });
    }
  }, [isServiceWorkerSupported, registerServiceWorker, subscribeToPushNotifications, hasNotificationPermission]);

  // Helper function to convert base64 to Uint8Array for VAPID key
  function urlBase64ToUint8Array(base64String: string) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  return {
    notifications,
    addNotification,
    removeNotification,
    requestNotificationPermission,
    hasNotificationPermission,
    subscribeToPushNotifications,
    unsubscribeFromPushNotifications,
    isPushSubscribed: !!pushSubscription
  };
};

// Custom hook to use notifications
const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

export { useNotification };
