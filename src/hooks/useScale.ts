import { useCallback, useContext, } from "react";
import { ChartContext } from "../contexts/chart.context";
import { AppContext } from "../contexts/app.context";


export function useScale() {
    const { valueRange, height } = useContext(ChartContext)
    const { timeRange, width } = useContext(AppContext)

    const v2y = useCallback((d: number) => {
        if (!valueRange || !height) return 0;
        const valueSpan = valueRange.max - valueRange.min;
        if (valueSpan === 0) return 0;
        // Invert Y-axis (0 at top, height at bottom)
        return height - ((d - valueRange.min) / valueSpan * height);
    }, [valueRange, height])

    const y2v = useCallback((c: number) => {
        if (!valueRange || !height) return 0;
        const valueSpan = valueRange.max - valueRange.min;
        if (valueSpan === 0) return 0;
        // Invert Y-axis (0 at top, height at bottom)
        return valueRange.min + (valueSpan * (height - c) / height);
    }, [valueRange, height])

    const t2x = useCallback((d: number) => {
        if (!timeRange || !width) return 0;
        const timeSpan = timeRange.max - timeRange.min;
        if (timeSpan === 0) return 0;
        return ((d - timeRange.min) / timeSpan) * width;
    }, [timeRange, width])

    const x2t = useCallback((c: number) => {
        if (!timeRange || !width) return 0;
        const timeSpan = timeRange.max - timeRange.min;
        if (timeSpan === 0) return 0;
        return timeRange.min + (timeSpan * c / width);
    }, [timeRange, width])

    const deltaX2t = useCallback((deltaX: number) => {
        if (!timeRange || !width) return 0;
        const timeSpan = timeRange.max - timeRange.min;
        if (timeSpan === 0) return 0;
        return timeSpan * deltaX / width;
    }, [timeRange, width])

    const deltaY2v = useCallback((deltaY: number) => {
        if (!valueRange || !height) return 0;
        const valueSpan = valueRange.max - valueRange.min;
        if (valueSpan === 0) return 0;
        return valueSpan * deltaY / height;
    }, [valueRange, height])

    const deltaT2x = useCallback((deltaT: number) => {
        if (!timeRange || !width) return 0;
        const timeSpan = timeRange.max - timeRange.min;
        if (timeSpan === 0) return 0;
        return (deltaT / timeSpan) * width;
    }, [timeRange, width])

    const deltaV2y = useCallback((deltaV: number) => {
        if (!valueRange || !height) return 0;
        const valueSpan = valueRange.max - valueRange.min;
        if (valueSpan === 0) return 0;
        return (deltaV / valueSpan) * height;
    }, [valueRange, height])

    return { v2y, y2v, t2x, x2t, deltaX2t, deltaY2v, deltaT2x, deltaV2y }
}
