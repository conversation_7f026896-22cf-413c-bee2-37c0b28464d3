import Konva from "konva";
import { isNil } from "es-toolkit";
import { useCallback } from "react";

/**
 * Hook that provides drawing utilities for plots
 */
export function usePlotDrawing() {
  /**
   * Draws a price line (last value line) with shadow effect
   */
  const drawPriceLine = useCallback(
    (context: Konva.Context, y: number, maxX: number, color: string) => {
      // Save the current context state
      context.save();

      // Set shadow properties for a lighter blurry shadow effect
      context.shadowColor = "rgba(0, 0, 0, 0.25)";
      context.shadowBlur = 5;
      context.shadowOffsetY = 2;
      context.shadowOffsetX = 0;

      // Draw price line with shadow
      context.beginPath();
      context.moveTo(0, y);
      context.lineTo(maxX, y);
      context.setAttr("strokeStyle", color);
      context.setAttr("lineWidth", 1);
      context.stroke();

      // Restore the context to remove shadow effect for subsequent drawings
      context.restore();
    },
    []
  );

  /**
   * Draws a simple line without any effects
   */
  const drawSimpleLine = useCallback(
    (
      context: Konva.Context,
      path: Array<{ x: number; y: number }>,
      color: string,
      lineWidth: number
    ) => {
      if (path.length < 2) return;

      context.save();

      // Draw a simple line
      context.beginPath();
      context.moveTo(path[0].x, path[0].y);
      for (let i = 1; i < path.length; i++) {
        context.lineTo(path[i].x, path[i].y);
      }
      context.strokeStyle = color;
      context.lineWidth = lineWidth;
      context.stroke();

      context.restore();
    },
    []
  );

  /**
   * Determines if a price line should be shown based on configuration and tick
   */
  const shouldShowPriceLine = useCallback(
    (config: Record<string, any> | undefined, tick: any): boolean => {
      return Boolean(config?.showPriceLine || (config?.isMain && tick));
    },
    []
  );

  /**
   * Gets the last valid value from a data point
   */
  const getLastValue = useCallback((dataPoint: any): number | undefined => {
    if (!dataPoint) return undefined;

    // For candlestick data
    if (!isNil(dataPoint.close)) {
      return dataPoint.close;
    }

    // For regular data points
    if (!isNil(dataPoint.value)) {
      return dataPoint.value;
    }

    return undefined;
  }, []);

  /**
   * Gets the color for a data point based on its properties and configuration
   */
  const getDataPointColor = useCallback(
    (dataPoint: any, config: Record<string, any> | undefined): string => {
      if (!dataPoint) return "#E8EFCF"; // Default color

      // If the data point has a specific color, use it
      if (dataPoint.color) return dataPoint.color;

      // For candlestick data
      if (!isNil(dataPoint.close) && !isNil(dataPoint.open)) {
        const change = dataPoint.close - dataPoint.open;
        return change > 0
          ? config?.riseColor || "#E8EFCF"
          : config?.fallColor || "#E8EFCF";
      }

      // For histogram data
      if (!isNil(dataPoint.value)) {
        return dataPoint.value >= 0
          ? config?.positiveColor || "#E8EFCF"
          : config?.negativeColor || "#E8EFCF";
      }

      // Default color
      return config?.defaultColor || "#E8EFCF";
    },
    []
  );

  return {
    drawPriceLine,
    drawSimpleLine,
    shouldShowPriceLine,
    getLastValue,
    getDataPointColor,
  };
}
