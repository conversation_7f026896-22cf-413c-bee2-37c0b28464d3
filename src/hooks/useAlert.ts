import { useCallback, useContext } from "react";
import { createAlertConfig, deleteAlertConfig, fetchAlertConfigs } from "../common/api";
import { AppContext } from "../contexts/app.context";
import { TDrawingConfig } from "../types";
import { ChartContext } from "../contexts/chart.context";

export function useAlert() {
    const { symbol, setAlertConfigList, timeframe } = useContext(AppContext)
    const { chartData } = useContext(ChartContext)

    const addPriceAlert = useCallback((price: number, triggerType: string) => {
        const alertConfig = {
            chart: chartData!.name,
            symbol,
            timeframe,
            enabled: true,
            triggerType,
            muted: false,
            general: false,
            arg1: {
                type: 'plot',
                name: 'price',
                refId: chartData!.mainPlot!.id,
            },
            arg2: {
                type: 'value',
                name: 'value',
                refId: 'value',
                value: price
            },
            op: {
                name: 'cross',
                type: 'cross'
            }
        };
        return createAlertConfig(alertConfig).then(() => {
            return fetchAlertConfigs().then(setAlertConfigList)
        })
    }, [symbol, chartData, timeframe, setAlertConfigList])

    const addDrawingAlert = useCallback((drawingConfig: TDrawingConfig, triggerType: string) => {
        const alertConfig = {
            chart: chartData!.name,
            symbol,
            timeframe,
            enabled: true,
            triggerType,
            muted: false,
            general: false,
            arg1: {
                type: 'plot',
                name: 'price',
                refId: chartData!.mainPlot!.id,
            },
            arg2: {
                type: 'drawing',
                name: drawingConfig.type,
                refId: drawingConfig.id!.toString()
            },
            op: {
                name: 'cross',
                type: 'cross'
            }
        };
        return createAlertConfig(alertConfig).then(() => {
            return fetchAlertConfigs().then(setAlertConfigList)
        })
    }, [symbol, chartData, timeframe, setAlertConfigList])

    const deleteAlert = useCallback((alertId: string) => {
        return deleteAlertConfig(alertId).then(() => {
            return fetchAlertConfigs().then(setAlertConfigList)
        })
    }, [setAlertConfigList])

    return { addPriceAlert, addDrawingAlert, deleteAlert };
}
