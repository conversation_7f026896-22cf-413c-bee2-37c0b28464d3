import React, { useEffect, useState, useRef, useCallback, useContext } from "react";
import { useNotification } from "../hooks/useNotification";
import { AppContext } from "../contexts/app.context";

// Type definition for stored notification data
interface StoredNotification {
    id: string;           // Unique identifier for the notification
    deadline: number;     // When the notification should trigger
    scheduledAt: number;  // When the notification was scheduled
    symbol: string;       // Symbol the notification is for
    timeframe: string;    // Timeframe the notification is for
    notificationSent: boolean; // Track if notification has been sent
}

// Local storage key
const NOTIFICATION_STORAGE_KEY = 'countdown_notifications';
// Debounce time to prevent multiple notifications (in ms)
const NOTIFICATION_DEBOUNCE_TIME = 5000;

export const Countdown = (props: { style?: React.CSSProperties, startTime?: number, deadline?: number }) => {
    const { startTime, deadline } = props;
    const { addNotification, requestNotificationPermission, hasNotificationPermission } = useNotification();
    const { symbol, timeframe } = useContext(AppContext);
    const [notificationPermissionRequested, setNotificationPermissionRequested] = useState(false);
    const [timeLeft, setTimeLeft] = useState(0);
    const [isNotificationScheduled, setIsNotificationScheduled] = useState(false);
    const nextDeadlineRef = useRef<number | null>(null);
    const lastNotificationTimeRef = useRef<number>(0); // Track when the last notification was sent

    // Format symbol for display (remove 'usdt' and uppercase)
    const formattedSymbol = symbol.replace('usdt', '').toUpperCase();

    const calculateTimeLeft = useCallback(() => {
        if (!startTime || !deadline) return 0;

        const currentTime = new Date().getTime();
        const timeDiff = deadline - currentTime;

        if (timeDiff <= 0) {
            // When time is up, start counting from startTime + interval again
            const interval = deadline - startTime;
            const elapsedIntervals = Math.floor((currentTime - startTime) / interval);
            const nextDeadline = startTime + (elapsedIntervals + 1) * interval;
            nextDeadlineRef.current = nextDeadline;
            return nextDeadline - currentTime;
        } else {
            nextDeadlineRef.current = deadline;
            return timeDiff;
        }
    }, [deadline, startTime]);

    // Generate a unique ID for notifications
    const generateNotificationId = useCallback(() => {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }, []);

    // Get all stored notifications
    const getStoredNotifications = useCallback((): StoredNotification[] => {
        const storedNotificationsJson = localStorage.getItem(NOTIFICATION_STORAGE_KEY);
        if (!storedNotificationsJson) return [];

        try {
            return JSON.parse(storedNotificationsJson);
        } catch (error) {
            console.error('Error parsing stored notifications:', error);
            return [];
        }
    }, []);

    // Save notification to localStorage
    const saveNotificationToStorage = useCallback((deadlineTime: number, notificationSent: boolean = false) => {
        const notificationId = generateNotificationId();
        const notificationData: StoredNotification = {
            id: notificationId,
            deadline: deadlineTime,
            scheduledAt: Date.now(),
            symbol,
            timeframe,
            notificationSent
        };

        // Get existing notifications
        const existingNotifications = getStoredNotifications();

        // Check if a notification with the same deadline already exists
        const existingIndex = existingNotifications.findIndex(n =>
            Math.abs(n.deadline - deadlineTime) < 1000 && n.symbol === symbol && n.timeframe === timeframe);

        if (existingIndex >= 0) {
            // Update existing notification
            existingNotifications[existingIndex] = notificationData;
        } else {
            // Add new notification
            existingNotifications.push(notificationData);
        }

        // Save back to localStorage
        localStorage.setItem(NOTIFICATION_STORAGE_KEY, JSON.stringify(existingNotifications));
        console.log(`Saved notification for ${symbol}/${timeframe}, sent: ${notificationSent}, id: ${notificationId}`);

        return notificationId;
    }, [symbol, timeframe, generateNotificationId, getStoredNotifications]);

    // Remove notification from localStorage by ID
    const removeNotificationById = useCallback((notificationId: string) => {
        const existingNotifications = getStoredNotifications();
        const filteredNotifications = existingNotifications.filter(n => n.id !== notificationId);

        localStorage.setItem(NOTIFICATION_STORAGE_KEY, JSON.stringify(filteredNotifications));
        console.log(`Removed notification with ID: ${notificationId}`);
    }, [getStoredNotifications]);

    // Mark notification as sent
    const markNotificationAsSent = useCallback((notificationId: string) => {
        const existingNotifications = getStoredNotifications();
        const updatedNotifications = existingNotifications.map(n => {
            if (n.id === notificationId) {
                return { ...n, notificationSent: true };
            }
            return n;
        });

        localStorage.setItem(NOTIFICATION_STORAGE_KEY, JSON.stringify(updatedNotifications));
        console.log(`Marked notification ${notificationId} as sent`);
    }, [getStoredNotifications]);

    // Set up notification checking interval
    useEffect(() => {
        // Function to check for notifications that need to be sent
        const checkNotifications = () => {
            const notifications = getStoredNotifications();
            const currentTime = Date.now();

            // Check if there's a notification for the current symbol/timeframe
            const currentNotification = notifications.find(
                n => n.symbol === symbol &&
                    n.timeframe === timeframe &&
                    !n.notificationSent &&
                    n.deadline > currentTime
            );

            // Update UI state based on whether there's a notification for current symbol/timeframe
            setIsNotificationScheduled(!!currentNotification);

            // Process all notifications that are due
            notifications.forEach(notification => {
                // Skip if already sent
                if (notification.notificationSent) return;

                const timeUntilDeadline = notification.deadline - currentTime;

                // If deadline has passed or is very close (within 1 second)
                if (timeUntilDeadline <= 1000 && timeUntilDeadline > -10000) { // Allow a 10-second grace period for missed notifications
                    // Check if enough time has passed since last notification
                    if (currentTime - lastNotificationTimeRef.current < NOTIFICATION_DEBOUNCE_TIME) {
                        console.log('Notification debounced - too soon after previous notification');
                        return;
                    }

                    // Update last notification time
                    lastNotificationTimeRef.current = currentTime;

                    // Format symbol for display
                    const formattedNotificationSymbol = notification.symbol.replace('usdt', '').toUpperCase();

                    // Create URL for the notification
                    const notificationUrl = `/${notification.symbol}-${notification.timeframe}`;

                    // Send notification with URL
                    addNotification({
                        message: `Countdown complete for ${formattedNotificationSymbol}/${notification.timeframe}!`,
                        type: "success",
                        duration: 10000,
                        useSystemNotification: true,
                        title: `${formattedNotificationSymbol}/${notification.timeframe} Countdown Complete`,
                        url: notificationUrl, // Add URL for service worker to use
                    });

                    // Mark as sent
                    markNotificationAsSent(notification.id);

                    // If this is for the current symbol/timeframe, update UI
                    if (notification.symbol === symbol && notification.timeframe === timeframe) {
                        setIsNotificationScheduled(false);
                    }
                }
                // Remove notifications that are too old (more than 10 seconds past deadline)
                else if (timeUntilDeadline < -10000) {
                    removeNotificationById(notification.id);
                }
            });
        };

        // Check immediately on mount or symbol/timeframe change
        checkNotifications();

        // Set up interval to check notifications every second
        const intervalId = setInterval(checkNotifications, 1000);

        // Cleanup function
        return () => {
            clearInterval(intervalId);
        };
    }, [
        symbol,
        timeframe,
        addNotification,
        getStoredNotifications,
        markNotificationAsSent,
        removeNotificationById
    ]);

    useEffect(() => {
        if (!startTime || !deadline) return;

        // Initial calculation
        const initialTimeLeft = calculateTimeLeft();
        setTimeLeft(initialTimeLeft);

        const interval = setInterval(() => {
            const newTimeLeft = calculateTimeLeft();
            setTimeLeft(newTimeLeft);
        }, 1000);

        return () => {
            clearInterval(interval);
            // We don't clear the notification timeout here as we want it to persist
            // across page reloads. The notification state is saved in localStorage
            // and will be restored on reload.
            // However, we do want to make sure we don't have duplicate timeouts
            // running, so we handle that in the other useEffect.
        }
    }, [deadline, startTime, calculateTimeLeft]);

    const formatTime = (milliseconds: number) => {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        return `${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
    };

    // Request notification permission if needed
    useEffect(() => {
        if (!notificationPermissionRequested && !hasNotificationPermission()) {
            requestNotificationPermission().then((granted: boolean) => {
                setNotificationPermissionRequested(true);
                if (granted) {
                    addNotification({
                        message: "Notification permission granted. You'll receive notifications even when using other apps.",
                        type: "success",
                        duration: 4000
                    });
                }
            });
        }
    }, [notificationPermissionRequested, hasNotificationPermission, requestNotificationPermission, addNotification]);

    const handleClick = async () => {
        // If notification is already scheduled, cancel it
        if (isNotificationScheduled) {
            // Find and remove the notification for the current symbol/timeframe
            const notifications = getStoredNotifications();
            const currentNotification = notifications.find(
                n => n.symbol === symbol && n.timeframe === timeframe && !n.notificationSent
            );

            if (currentNotification) {
                removeNotificationById(currentNotification.id);
            }

            setIsNotificationScheduled(false);

            addNotification({
                message: `${formattedSymbol}/${timeframe} countdown notification canceled`,
                type: "success",
                duration: 2000
            });
            return;
        }

        if (!nextDeadlineRef.current) return;

        // Calculate time until next deadline
        const currentTime = new Date().getTime();
        const timeUntilDeadline = nextDeadlineRef.current - currentTime;

        if (timeUntilDeadline <= 0) {
            addNotification({
                message: `Cannot schedule notification - deadline already passed for ${formattedSymbol}/${timeframe}`,
                type: "error",
                duration: 3000
            });
            return;
        }

        // Request notification permission if not already granted
        let permissionGranted = hasNotificationPermission();
        if (!permissionGranted) {
            permissionGranted = await requestNotificationPermission();
            setNotificationPermissionRequested(true);
        }

        // Schedule notification
        setIsNotificationScheduled(true);

        // Save notification to localStorage for persistence with current symbol/timeframe
        saveNotificationToStorage(nextDeadlineRef.current, false);

        addNotification({
            message: `${formattedSymbol}/${timeframe} notification scheduled for ${formatTime(timeUntilDeadline)}`,
            type: "success",
            duration: 2000
        });

        console.log(`Scheduled notification for ${symbol}/${timeframe} at ${new Date(nextDeadlineRef.current).toLocaleTimeString()}`);
    };

    if (!startTime || !deadline) return null;

    return (
        <div
            onClick={handleClick}
            style={{
                color: isNotificationScheduled ? 'rgb(207, 81, 7)' : 'rgb(7, 81, 207)',
                padding: "6px",
                fontSize: "15px",
                fontWeight: 'bold',
                textAlign: "center",
                cursor: "pointer",
                ...props.style
            }}
            title={isNotificationScheduled
                ? `Click to cancel ${formattedSymbol}/${timeframe} notification`
                : `Click to schedule ${formattedSymbol}/${timeframe} notification`
            }
        >
            {formatTime(timeLeft)}
        </div>
    );
};

