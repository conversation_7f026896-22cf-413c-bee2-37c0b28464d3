import { useContext, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolboxContainer, ToolboxItem, ToolboxTooltip } from '../styles/toolbox.styles';
import RayIcon from '../icons/ray.icon';
import PriceRangeIcon from '../icons/priceRange.icon';
import { AppContext } from "../contexts/app.context";
import { ETool } from "../types";
import RangeTradeDistIcon from "../icons/rangeTradeDist.icon";
import HorizontalRayChannelIcon from "../icons/horizontalRayChannel.icon";
import { useDataContext } from "../contexts/data.context";
import { useNotification } from "../hooks/useNotification";


export const Toolbox = () => {
  const [hoveredTool, setHoveredTool] = useState<ETool | null>(null);
  const { activeTool, setActiveTool } = useContext(AppContext)
  const { clearDrawingList } = useDataContext()
  const { addNotification } = useNotification()
  // Define the tools with their icons and tooltips
  const tools = [
    { id: ETool.Ray, icon: RayIcon, tooltip: 'Ray' },
    { id: ETool.PriceRange, icon: PriceRangeIcon, tooltip: 'Price Range' },
    { id: ETool.HorizontalRayChannel, icon: HorizontalRayChannelIcon, tooltip: 'Horizontal Ray Channel' },
    { id: ETool.RangeTradeDist, icon: RangeTradeDistIcon, tooltip: 'Range Trade Distribution' },

    // Add more tools here as needed
  ];

  const handleToolClick = (toolId: ETool) => {
    if (activeTool === toolId) {
      setActiveTool(ETool.None)
    } else {
      setActiveTool(toolId)
    }
  };

  return (
    <ToolboxContainer>
      {tools.map((tool) => {
        const isActive = activeTool === tool.id;
        const isHovered = hoveredTool === tool.id;

        return (
          <div
            key={tool.id}
            style={{ position: 'relative' }}
            onMouseEnter={() => setHoveredTool(tool.id)}
            onMouseLeave={() => setHoveredTool(null)}
          >
            <ToolboxItem
              active={isActive}
              onClick={() => handleToolClick(tool.id)}
            >
              <tool.icon color="rgb(7, 81, 207)" size={24} />
            </ToolboxItem>

            <ToolboxTooltip
              style={{
                opacity: isHovered ? 1 : 0,
                transform: isHovered ? 'translateY(-50%)' : 'translateY(-50%) translateX(-10px)',
              }}
            >
              {tool.tooltip}
            </ToolboxTooltip>
          </div>
        );
      })}
      <PullChain onClick={async () => {
        await clearDrawingList()
          .then(() => {
            addNotification({
              title: 'Drawing List Cleared',
              message: 'The drawing list has been cleared',
              type: 'success',
              duration: 2000
            })
          })
          .catch((error) => {
            addNotification({
              title: 'Error',
              message: 'Failed to clear drawing list ' + error.message,
              type: 'error',
              duration: 2000
            })
          })
      }} />
    </ToolboxContainer>
  );
};
