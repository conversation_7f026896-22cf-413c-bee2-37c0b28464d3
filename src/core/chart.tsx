import { useContext, useEffect, useState } from "react";
import { type ChartData } from "../storage/chartData";
import { ChartContainer } from "../styles/chart.styles";
import { AppContext } from "../contexts/app.context";
import { ChartContext } from "../contexts/chart.context";
import { YAxis } from "./yAxis";
import { Pane } from "./pane";
import { TRange } from "../types";
import { DEFAULT_GAP_RATIO } from "../common/constants";

export const Chart = (props: { chartData: ChartData, height: number, style?: React.CSSProperties | undefined }) => {
    const [autoScale, setAutoScale] = useState(true)
    const [valueRange, setValueRange] = useState<TRange>({ max: 0, min: 0 })
    const { timeRange } = useContext(AppContext)

    // init load
    useEffect(() => {
        if (!props.chartData || !timeRange) return
        const valueRange = props.chartData.getValueRangeByTime(timeRange.min, timeRange.max)
        if (valueRange) {
            const span = valueRange.max - valueRange.min
            const padding = span * DEFAULT_GAP_RATIO
            setValueRange({
                min: valueRange.min - padding,
                max: valueRange.max + padding
            })
        }
    }, [props.chartData])

    return (
        <ChartContext.Provider value={{
            valueRange: valueRange,
            setValueRange: setValueRange,
            autoScale,
            setAutoScale,
            chartData: props.chartData,
            height: props.height,
        }}>
            <ChartContainer style={props.style}>
                <Pane />
                <YAxis />
            </ChartContainer>
        </ChartContext.Provider>
    )
}
