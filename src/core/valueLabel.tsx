import { Label, Tag, Text } from "react-konva";
import { useEffect, useRef, useState } from "react";
import Konva from "konva";
import { useScale } from "../hooks/useScale";
import { getFormatNumStr } from "../common/util";


export default function ValueLabel(props: {
    visible?: boolean,
    value?: number,
    y?: number,
    fontColor?: string,
    backgroundColor?: string,
    borderColor?: string,
    suffix?: string
}) {
    const { v2y, y2v } = useScale()
    const labelRef = useRef<Konva.Label>(null)
    const [labelY, setLabelY] = useState(0)
    const [labelText, setLabelText] = useState('')
    const [isBlinking, setIsBlinking] = useState(false)
    const [prevPrice, setPrevPrice] = useState<number | undefined>(undefined)
    const blinkTimeoutRef = useRef<number | null>(null)

    useEffect(() => {
        const labelHeight = labelRef.current && labelRef.current.getText() ? labelRef.current.getText().height() : 0;
        const calculatedY = props.y !== null && props.y !== undefined ? props.y : v2y(props.value ?? 0);
        const value = Math.max(calculatedY - labelHeight / 2, 0);
        setLabelY(value);
        setLabelText(getFormatNumStr(props.value ?? y2v(calculatedY)));
    }, [props.value, props.y, v2y, y2v]);

    // Detect price changes and trigger blink animation
    useEffect(() => {
        // Only trigger blink if we have a previous price to compare with
        if (prevPrice !== undefined && props.value !== undefined && props.value !== prevPrice) {
            // Clear any existing timeout
            if (blinkTimeoutRef.current) {
                clearTimeout(blinkTimeoutRef.current);
            }

            // Start blinking
            setIsBlinking(true);

            // Stop blinking after 500ms
            blinkTimeoutRef.current = setTimeout(() => {
                setIsBlinking(false);
            }, 400);
        }

        // Update previous price
        setPrevPrice(props.value);

        // Clean up timeout on unmount
        return () => {
            if (blinkTimeoutRef.current) {
                clearTimeout(blinkTimeoutRef.current);
            }
        };
    }, [props.value]);


    // Determine colors based on blinking state
    const getColors = () => {
        if (!isBlinking) {
            // Normal state
            return {
                backgroundColor: props.backgroundColor ?? "black",
                fontColor: props.fontColor ?? "white",
                borderColor: props.borderColor ?? "white"
            };
        } else {
            // Blinking state - swap background and font colors
            return {
                backgroundColor: props.fontColor ?? "white",
                fontColor: props.backgroundColor ?? "black",
                borderColor: props.borderColor ?? "white"
            };
        }
    };

    const colors = getColors();

    return (
        <Label visible={props.visible ?? true} x={1} y={labelY} ref={labelRef}>
            <Tag
                fill={colors.backgroundColor}
                cornerRadius={[0, 3, 3, 0]}
                stroke={colors.borderColor}
                strokeWidth={1}
                shadowColor="rgba(0, 0, 0, 0.25)"
                shadowBlur={5}
                shadowOffsetY={1}
                shadowOffsetX={0}
            />
            <Text
                align="center"
                text={`${labelText} ${props.suffix ? '\n\n' + props.suffix : ''}`}
                fontSize={12}
                fontStyle="bold"
                padding={8}
                fill={colors.fontColor}
            />
        </Label>
    )
}