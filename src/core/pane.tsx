import { useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { AppContext } from '../contexts/app.context';
import { ChartContext } from '../contexts/chart.context';
import { useScale } from '../hooks/useScale';
import { Layer, Stage } from 'react-konva';
import Konva from 'konva';
import { type ChartData } from '../storage/chartData';
import { CandlestickPlot } from '../plots/candlestick.plot';
import { CandlestickPlotData } from '../storage/candlestick.pd';
import { Crosshairs } from './crosshairs';
import { HistogramPlot } from '../plots/histogram.plot';
import { HistogramPlotData } from '../storage/histogram.pd';
import { LinePlot } from '../plots/line.plot';
import { LinePlotData } from '../storage/line.pd';
import { Ray } from '../drawings/ray';
import { useContextMenu } from '../hooks/useContextMenu';
import { ContextMenu } from '../components/contextMenu';
import { AlertVisualizer } from '../components/alertVisualizer';
import { PaneContext } from '../contexts/pane.context';
import { DEFAULT_GAP_RATIO, MAIN_CHART_NAME, } from '../common/constants';
import { ETool, EPlot, TDrawingConfig } from '../types';
import { PriceRange } from '../drawings/priceRange';
import { RangeTradeDist } from '../drawings/rangeTradeDist';
import { HorizontalRayChannel } from '../drawings/horizontalRayChannel';
import { useDataContext } from '../contexts/data.context';



export const Pane = () => {
    const { timeRange, setTimeRange, setActiveChart, activeChart, setPointerCoord, activeTool, symbol, width, timeUnit, timeframe } = useContext(AppContext)
    const { setValueRange, autoScale, chartData, height } = useContext(ChartContext)
    const { contextMenuState, menuRef, onContextMenu, closeContextMenu } = useContextMenu()
    const stageRef = useRef<Konva.Stage>(null)
    const { deltaY2v, deltaX2t } = useScale()
    const { drawingConfigList, reloadDrawingConfigList } = useDataContext()
    const [selectedDrawingConfig, setSelectedDrawingConfig] = useState<TDrawingConfig | null>(null)

    const pan = useCallback((deltaX: number, deltaY: number) => {
        if (deltaX !== 0) {
            const timeDelta = deltaX2t(deltaX)

            setTimeRange({
                max: timeRange.max - timeDelta,
                min: timeRange.min - timeDelta
            });
        }


        if (!autoScale && deltaY !== 0) {
            const valueDelta = deltaY2v(deltaY)
            setValueRange((prev) => {
                return {
                    min: prev.min + valueDelta,
                    max: prev.max + valueDelta
                }
            })
        }
    }, [timeRange, autoScale, setTimeRange, setValueRange])

    const zoom = useCallback((wheelDelta: number) => {
        // Determine zoom factor based on wheel delta
        const zoomFactor = Math.pow(0.999, wheelDelta);

        setTimeRange(prev => {
            const timeSpan = prev.max - prev.min
            const newTimeSpan = timeSpan * zoomFactor
            const centerTime = prev.min + (timeSpan / 2)
            const newMin = centerTime - (newTimeSpan / 2)
            const newMax = centerTime + (newTimeSpan / 2)

            // Check if the new range would exceed the 2000 bar limit
            const barsInView = (newMax - newMin) / timeUnit;
            if (barsInView > 2000 && zoomFactor > 1) {
                // If zooming out would exceed limit, maintain current range
                return prev;
            }

            return {
                min: newMin,
                max: newMax
            }
        })
    }, [setTimeRange, timeUnit])

    useEffect(() => {
        if (autoScale) {
            if (!chartData || !timeRange) return
            const valueRange = chartData.getValueRangeByTime(timeRange.min, timeRange.max)
            if (valueRange) {
                const span = valueRange.max - valueRange.min
                const padding = span * DEFAULT_GAP_RATIO
                setValueRange({
                    min: valueRange.min - padding,
                    max: valueRange.max + padding
                })
            }
        }
    }, [autoScale, timeRange])

    const newDrawing = useMemo(() => {

        if (activeTool && activeChart === chartData?.name) {
            const defaultConfig = { symbol, chart: chartData?.name ?? '', timeframe: timeframe, type: activeTool, dataPoints: [] }
            switch (activeTool) {
                case ETool.Ray:
                    return <Ray config={defaultConfig} onDrawEnd={reloadDrawingConfigList} />
                case ETool.PriceRange:
                    return <PriceRange config={defaultConfig} onDrawEnd={reloadDrawingConfigList} />
                case ETool.RangeTradeDist: {
                    if (chartData?.name === MAIN_CHART_NAME) {
                        return <RangeTradeDist config={defaultConfig} onDrawEnd={reloadDrawingConfigList} />
                    }
                    return null
                }
                case ETool.HorizontalRayChannel:
                    return <HorizontalRayChannel config={defaultConfig} onDrawEnd={reloadDrawingConfigList} />
                default:
                    return null
            }
        }
        return null
    }, [activeTool, activeChart, chartData, timeframe])

    const drawings = useMemo(() => {
        return drawingConfigList.filter(config => config.chart === chartData?.name).map((config) => {
            switch (config.type) {
                case ETool.Ray:
                    return <Ray key={config.id} config={config} />
                case ETool.PriceRange:
                    return <PriceRange key={config.id} config={config} />
                case ETool.RangeTradeDist: {
                    if (chartData?.name === MAIN_CHART_NAME && config.timeframe === timeframe) {
                        return <RangeTradeDist key={config.id} config={config} />
                    }
                    return null
                }
                case ETool.HorizontalRayChannel: {
                    return <HorizontalRayChannel key={config.id} config={config} />
                }

                default:
                    return null
            }
        })
    }, [drawingConfigList])


    return (
        <PaneContext.Provider value={{
            selectedDrawingConfig,
            setSelectedDrawingConfig,
            drawingConfigList
        }}>
            <div style={{ position: 'relative', width, height }}>
                <Stage
                    ref={stageRef}
                    draggable
                    dragBoundFunc={function () {
                        return {
                            x: this.absolutePosition().x,
                            y: this.absolutePosition().y
                        }
                    }}
                    width={width}
                    height={height}
                    // onClick={onClick}
                    onWheel={onWheel}
                    onDragMove={onDragMove}
                    onMouseEnter={onMouseEnter}
                    onMouseLeave={onMouseLeave}
                    onMouseMove={onMouseMove}
                    onContextMenu={onContextMenu}
                >
                    <Layer>
                        {chartData && renderPlots(chartData)}
                    </Layer>
                    <Layer>
                        <Crosshairs />
                    </Layer>
                    <Layer>
                        {newDrawing}
                        {drawings}
                    </Layer>
                    <Layer>
                        {/* {
                            newToolConfig && newToolConfig.type === ETool.RangeTradeDist ?
                                <TradeDistRange config={newToolConfig} /> : null
                        }
                        {tradeDists.map((dist, index) => (
                            <TradeDistPlot key={`trade-dist-${dist.timeRange.min}-${dist.timeRange.max}-${index}`} data={dist.data} timeRange={dist.timeRange} />
                        ))} */}
                        <AlertVisualizer />
                    </Layer>
                </Stage>

                {/* Context Menu */}
                {contextMenuState && (
                    <ContextMenu
                        state={contextMenuState}
                        menuRef={menuRef}
                        onClose={closeContextMenu}

                    />
                )}
            </div>
        </PaneContext.Provider>
    )

    function renderPlots(chartData: ChartData) {
        const plotList = []
        for (const plotData of chartData.plotDataList) {
            switch (plotData.type) {
                case EPlot.Candlestick:
                    plotList.push(<CandlestickPlot key={plotData.id} plotData={plotData as CandlestickPlotData} />)
                    break;
                case EPlot.Histogram:
                    plotList.push(<HistogramPlot key={plotData.id} plotData={plotData as HistogramPlotData} />)
                    break;
                case EPlot.Line:
                    plotList.push(<LinePlot key={plotData.id} plotData={plotData as LinePlotData} />)
                    break;
                default:
                    break;
            }
        }
        return plotList
    }

    function onWheel(e: Konva.KonvaEventObject<WheelEvent>) {
        e.cancelBubble = true
        const wheelDelta = e.evt.deltaY;
        const wheelDeltaX = e.evt.deltaX;

        // Ignore horizontal scroll attempts
        if (Math.abs(wheelDeltaX) > Math.abs(wheelDelta)) return;

        zoom(-wheelDelta);
    }

    function onDragMove(e: Konva.KonvaEventObject<DragEvent>) {
        e.cancelBubble = true

        const stage = e.target.getStage() as Konva.Stage
        const mousePos = stage.getPointerPosition() as Konva.Vector2d
        setPointerCoord(mousePos)

        if (e.target !== stageRef.current) return

        pan(e.evt.movementX, e.evt.movementY)
    }

    function onMouseEnter() {
        if (chartData) {
            setActiveChart(chartData.name)
            setPointerCoord(null)
        }
    }
    function onMouseLeave() {
        if (activeChart === chartData?.name) {
            setActiveChart('')
        }
    }

    function onMouseMove(e: Konva.KonvaEventObject<MouseEvent>) {
        const stage = e.target.getStage() as Konva.Stage;
        const mousePos = stage.getPointerPosition() as Konva.Vector2d;

        // Update pointer coordinates for crosshairs
        if (activeChart === chartData?.name) {
            setPointerCoord(mousePos);
        } else {
            setPointerCoord(null);
        }
    }
}
