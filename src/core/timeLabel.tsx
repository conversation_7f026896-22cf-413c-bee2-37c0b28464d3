import { Label, Tag, Text } from "react-konva";
import { useEffect, useRef, useState } from "react";
import Konva from "konva";
import { useScale } from "../hooks/useScale";
import { format } from "date-fns";


export default function TimeLabel(props: {
    visible?: boolean,
    time?: number,
    x?: number,
    fontColor?: string,
    backgroundColor?: string,
    borderColor?: string,
}) {
    const { x2t, t2x } = useScale();
    const labelRef = useRef<Konva.Label>(null);
    const [labelX, setLabelX] = useState(0);
    const [labelText, setLabelText] = useState('');

    useEffect(() => {
        const labelWidth = labelRef.current && labelRef.current.getText() ? labelRef.current.getText().width() : 0;
        const calculatedX = props.x !== null && props.x !== undefined ? props.x : t2x(props.time ?? 0);
        const value = Math.max(Math.min(calculatedX - labelWidth / 2, window.innerWidth - labelWidth - 5), 5);
        setLabelX(value);
        setLabelText(format(x2t(calculatedX), 'MM/dd HH:mm'));
    }, [labelRef.current, props.time, props.x, t2x, x2t]);

    return (
        <Label
            ref={labelRef}
            x={labelX}
            visible={props.visible !== false}
        >
            <Tag
                fill={props.backgroundColor || '#3C3D37'}
                stroke={props.borderColor || '#3C3D37'}
                strokeWidth={1}
                cornerRadius={2}
                shadowColor="rgba(0, 0, 0, 0.25)"
                shadowBlur={5}
                shadowOffsetY={1}
                shadowOffsetX={0}
            />
            <Text
                text={labelText}
                padding={8}
                fill={props.fontColor || 'white'}
                fontSize={12}
                fontStyle="bold"
            />
        </Label>
    );
}