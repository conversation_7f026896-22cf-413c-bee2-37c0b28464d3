import { Layer, Stage } from 'react-konva'
import { useCallback, useContext } from 'react'
import Konva from 'konva'
import { ChartContext } from '../contexts/chart.context'
import ValueLabel from './valueLabel'
import { AppContext } from '../contexts/app.context'
import { useScale } from '../hooks/useScale'
import { getPct } from '../common/util'
import { YAXIS_WIDTH, MAIN_CHART_NAME, CROSSHAIR_COLOR } from '../common/constants'

export function YAxis() {
    const { height, setValueRange, setAutoScale, chartData } = useContext(ChartContext)
    const { crosshairsCoord, activeChart, tick } = useContext(AppContext)
    const { y2v } = useScale()

    const zoom = useCallback((wheelDelta: number) => {
        // Determine zoom factor based on wheel delta
        const zoomFactor = Math.pow(0.999, -wheelDelta);

        setValueRange(prev => {
            const span = prev.max - prev.min
            const newSpan = span * zoomFactor
            const centerTime = prev.min + (span / 2)
            const newMin = centerTime - (newSpan / 2)
            const newMax = centerTime + (newSpan / 2)
            return {
                min: newMin,
                max: newMax
            }
        })
        setAutoScale(false)
    }, [setValueRange])

    return (
        <Stage
            width={YAXIS_WIDTH}
            height={height}
            onWheel={onWheel}
            onDblClick={onDblClick}
        >
            <Layer>
                {
                    tick && chartData?.plotDataList.map((plotData: any) => {
                        if (plotData.config?.showPriceLine || plotData.config?.isMain) {
                            return <ValueLabel
                                key={`label-${plotData.id}`}
                                value={plotData.last.y ?? plotData.last.close}
                                backgroundColor="white"
                                fontColor={plotData.last.color}
                                borderColor={plotData.last.color}
                            />
                        }
                    })
                }
            </Layer>
            <Layer>
                {
                    crosshairsCoord.current &&
                    <ValueLabel
                        y={crosshairsCoord?.current?.y}
                        borderColor={CROSSHAIR_COLOR}
                        backgroundColor={CROSSHAIR_COLOR}
                        visible={activeChart === chartData?.name}
                        suffix={
                            chartData && chartData.name === MAIN_CHART_NAME && tick && crosshairsCoord.current ?
                                `(${getPct((y2v(crosshairsCoord.current.y) - tick.price) / tick.price)})` :
                                ''
                        }
                    />
                }

            </Layer>
        </Stage>
    )

    function onWheel(e: Konva.KonvaEventObject<WheelEvent>) {
        e.evt.preventDefault()
        zoom(e.evt.deltaY)
    }

    function onDblClick(_e: Konva.KonvaEventObject<MouseEvent>) {
        setAutoScale(true)
    }
}
