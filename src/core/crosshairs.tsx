import { useContext, useEffect, useState } from "react";
import { Group, Line } from "react-konva";
import { AppContext } from "../contexts/app.context";
import { ChartContext } from "../contexts/chart.context";
import { findClosestDivisible } from "../common/util";
import { useScale } from "../hooks/useScale";
import { CROSSHAIR_COLOR } from "../common/constants";

export function Crosshairs() {
    const { activeChart, pointerCoord, crosshairsCoord, timeUnit, width } = useContext(AppContext)
    const { chartData, height } = useContext(ChartContext)
    const { x2t, y2v, t2x } = useScale()
    const [, setRender] = useState({});


    useEffect(() => {
        if (!pointerCoord) {
            crosshairsCoord.current = null
            return
        }
        if (activeChart !== chartData?.name) return

        const coord = {
            x: t2x(findClosestDivisible(x2t(pointerCoord.x), timeUnit)),
            y: pointerCoord.y
        }
        crosshairsCoord.current = coord
        setRender({})
    }, [pointerCoord, activeChart, timeUnit, x2t, y2v, t2x]);


    return (
        crosshairsCoord.current && (
            <Group listening={false}>
                <Line
                    points={[0, crosshairsCoord.current.y, width, crosshairsCoord.current.y]}
                    visible={activeChart !== '' && activeChart === chartData?.name}
                    strokeWidth={1.5}
                    stroke={CROSSHAIR_COLOR}
                    dash={[4, 4]}
                />
                <Line
                    points={[crosshairsCoord.current.x, 0, crosshairsCoord.current.x, height]}
                    visible={activeChart !== ''}
                    strokeWidth={1.5}
                    stroke={CROSSHAIR_COLOR}
                    dash={[4, 4]}
                />
            </Group>
        )
    );
}
