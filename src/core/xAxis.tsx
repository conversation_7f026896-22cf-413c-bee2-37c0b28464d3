import { Layer, Stage } from 'react-konva'
import { useContext } from 'react'
import { AppContext } from '../contexts/app.context'
import TimeLabel from './timeLabel'
import { XAXIS_HEIGHT, CROSSHAIR_COLOR } from '../common/constants'

export function XAxis(props: { width: number }) {
    const { crosshairsCoord, activeChart } = useContext(AppContext)
    return (
        <Stage
            width={props.width}
            height={XAXIS_HEIGHT}
        >
            <Layer>
                {
                    crosshairsCoord.current &&
                    <TimeLabel
                        x={crosshairsCoord?.current?.x}
                        borderColor={CROSSHAIR_COLOR}
                        backgroundColor={CROSSHAIR_COLOR}
                        visible={activeChart !== ''}
                    />
                }
            </Layer>
        </Stage>
    )
}
