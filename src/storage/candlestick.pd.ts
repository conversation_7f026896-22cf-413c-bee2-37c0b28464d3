
import { TCandlestick, EPlot } from "../types";
import ListPlotData from "./list.pd";

export class CandlestickPlotData extends ListPlotData<TCandlestick> {
    readonly type = EPlot.Candlestick;

    getValueRangeByTime(fromX: number, toX: number) {
        const selected = this.selectByTime(fromX, toX);
        if (selected.length === 0) return

        let max = -Infinity;
        let min = Infinity;
        for (const dp of selected) {
            if (dp.high > max) max = dp.high;
            if (dp.low < min) min = dp.low;
        }
        return { max, min };
    }
}