
import { TChartData, TStreamPlotData, EPlot, TPlotData } from "../types";
import { CandlestickPlotData } from "./candlestick.pd";
import { HistogramPlotData } from "./histogram.pd";
import { LinePlotData } from "./line.pd";

export class ChartData {
    readonly name: string;
    readonly plotDataMap: Map<string, LinePlotData | CandlestickPlotData | HistogramPlotData>;
    private mainPlotId?: string;

    constructor(data: TChartData) {
        this.name = data.name
        this.plotDataMap = new Map()

        for (const plot of data.plots) {
            this.plotDataMap.set(plot.id, this.createPlotData(plot))
        }
    }

    updateData(data: TStreamPlotData) {
        const plotData = this.plotDataMap.get(data.id)
        switch (plotData?.type) {
            case EPlot.Line:
            case EPlot.Candlestick:
            case EPlot.Histogram:
                plotData?.update(data.data[0])
                break;
            default:
                break;
        }

    }

    get mainPlot() {
        if (!this.mainPlotId) {
            this.mainPlotId = this.plotDataList?.find(pd => pd.config?.isMain)?.id
        }
        if (!this.mainPlotId) return
        return this.plotDataMap.get(this.mainPlotId)
    }

    appendData(data: TChartData) {
        for (const plot of data.plots) {
            const plotData = this.plotDataMap.get(plot.id)
            switch (plot.type) {
                case EPlot.Line:
                case EPlot.Candlestick:
                case EPlot.Histogram:
                    plotData?.append(plot.data as any)
                    break;

                default:
                    break;
            }
        }
    }

    prependData(data: TChartData) {
        for (const plot of data.plots) {
            const plotData = this.plotDataMap.get(plot.id)
            switch (plot.type) {
                case EPlot.Line:
                case EPlot.Candlestick:
                case EPlot.Histogram:
                    plotData?.prepend(plot.data as any)
                    break;
                default:
                    break;
            }
        }
    }

    createPlotData(plot: TPlotData) {
        switch (plot.type) {
            case EPlot.Line:
                return new LinePlotData(plot.id, plot.data, plot.config)
            case EPlot.Candlestick:
                return new CandlestickPlotData(plot.id, plot.data, plot.config)
            case EPlot.Histogram:
                return new HistogramPlotData(plot.id, plot.data, plot.config)
            default:
                throw new Error('invalid plot type')
        }
    }

    getTimeRange() {
        let max = -Infinity;
        let min = Infinity;

        this.plotDataMap.forEach((plotData) => {
            if (plotData.length === 0) return
            if (plotData.last.time > max) max = plotData.last.time
            if (plotData.first.time < min) min = plotData.first.time
        })
        if (max === -Infinity || min === Infinity) return
        return {
            max,
            min
        }
    }

    getValueRangeByTime(from: number, to: number) {
        let max = -Infinity;
        let min = Infinity;

        this.plotDataMap.forEach((plotData) => {
            const range = plotData.getValueRangeByTime(from, to)
            if (range) {
                if (range.max > max) max = range.max
                if (range.min < min) min = range.min
            }
        })
        if (max === -Infinity || min === Infinity) return
        return {
            max,
            min
        }
    }

    get plotDataList() {
        return [...this.plotDataMap.values()]
    }
}