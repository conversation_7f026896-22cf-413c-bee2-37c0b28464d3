
import { PlotData } from "./plotData";
import { TRange } from "../types";


export default abstract class ListPlotData<T extends { time: number }> extends PlotData<T[]> {

    get list() {
        return Object.freeze([...this.srcData])
    }
    get length() {
        return this.srcData.length
    }

    get last() {
        return this.srcData[this.length - 1]
    }
    get first() {
        return this.srcData[0]
    }

    update(item: T) {
        if (this.srcData[this.srcData.length - 1].time === item.time) {
            this.srcData[this.srcData.length - 1] = item
        } else if (item.time > this.srcData[this.srcData.length - 1].time) {
            this.srcData.push(item)
        }
    }
    prepend(items: T[]) {
        if (items[items.length - 1].time === this.first.time) {
            this.srcData.unshift(...items.slice(0, items.length - 1))
        } else if (items[items.length - 1].time < this.first.time) {
            this.srcData.unshift(...items)
        }
    }

    append(items: T[]) {
        if (items[0].time === this.last.time) {
            this.srcData.push(...items.slice(1, items.length))
        } else if (items[0].time > this.last.time) {
            this.srcData.push(...items)
        }
    }

    selectByTime(from: number, to: number) {
        return this.srcData.filter(dp => dp.time >= from && dp.time <= to)
    }

    selectByIndex(fromIndex: number, toIndex?: number) {
        if (!toIndex) return this.srcData.slice(fromIndex)
        return this.srcData.slice(fromIndex, toIndex + 1)
    }

    selectOneByTime(time: number) {
        return this.srcData.find(dp => dp.time === time)
    }

    getTimeRangeByIndex(fromIndex: number, toIndex?: number) {
        const selected = this.selectByIndex(fromIndex, toIndex)
        return {
            max: selected[selected.length - 1].time,
            min: selected[0].time,
        }
    }

    abstract getValueRangeByTime(fromX: number, toX: number): TRange | void
    // abstract get tick(): { time: number, value: number }
}
