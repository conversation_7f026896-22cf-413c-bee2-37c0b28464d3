import { styled } from '@stitches/react';
import { keyframes } from '@stitches/react';

// Subtle twist animation for buttons on hover
const twistAnimation = keyframes({
  '0%': { transform: 'rotate(0deg)' },
  '25%': { transform: 'rotate(1deg)' },
  '75%': { transform: 'rotate(-1deg)' },
  '100%': { transform: 'rotate(0deg)' }
});

// Pulse animation for loading state
const pulseAnimation = keyframes({
  '0%': { opacity: 1 },
  '50%': { opacity: 0.6 },
  '100%': { opacity: 1 }
});

// Progress bar animation for loading state
const progressAnimation = keyframes({
  '0%': { width: '0%' },
  '50%': { width: '100%' },
  '100%': { width: '0%' }
});

// Main outer container that holds both the panel and positions list
export const TradingModuleContainer = styled('div', {
  position: 'absolute',
  zIndex: 1000,
  userSelect: 'none',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center', // Center children horizontally
  gap: '0', // No gap between panel and positions list
  minWidth: '220px', // Ensure minimum width for the module
  // Default position is now set in the component using defaultPosition

  // Default position if not being dragged
  variants: {
    isDragging: {
      true: {
        cursor: 'grabbing',
      },
      false: {
        cursor: 'grab',
      }
    }
  },

  defaultVariants: {
    isDragging: false
  }
});

// The actual trading panel
export const TradingPanelContainer = styled('div', {
  backgroundColor: 'rgba(255, 255, 255, 0.95)',
  borderRadius: '4px',
  border: '1px solid rgba(7, 81, 207, 0.3)',
  padding: '8px',
  minWidth: '180px',
  width: '100%', // Take up full width of parent container
  backdropFilter: 'blur(5px)',
  zIndex: 1000,
  boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
  position: 'relative', // Needed for the shadow to overlay properly
});

export const TradingPanelHeader = styled('div', {
  display: 'flex',
  justifyContent: 'flex-start', // Changed from space-between to flex-start
  alignItems: 'center',
  marginBottom: '8px',
  color: 'rgb(7, 81, 207)',
  fontWeight: 'bold',
  fontSize: '14px',
  fontFamily: 'Zed, sans-serif',
  cursor: 'grab',

  '&:active': {
    cursor: 'grabbing',
  },

  // Visual indicator that it's draggable
  '&::before': {
    content: '⋮⋮',
    marginRight: '6px',
    fontSize: '10px',
    opacity: 0.5,
  },

  // Style for child elements
  '& > div:first-child': {
    flex: '1',
  }
});


export const InputContainer = styled('div', {
  marginBottom: '8px',
});

export const AmountInput = styled('input', {
  width: '100%',
  padding: '6px 8px',
  borderRadius: '4px',
  border: '1px solid rgba(7, 81, 207, 0.3)',
  backgroundColor: 'rgba(255, 255, 255, 0.8)',
  color: 'rgb(7, 81, 207)',
  fontSize: '12px',
  fontWeight: 'bold',
  outline: 'none',
  transition: 'all 0.2s ease',

  '&:focus': {
    borderColor: 'rgba(7, 81, 207, 0.6)',
    boxShadow: '0 0 0 2px rgba(7, 81, 207, 0.1)',
  },

  '&::placeholder': {
    color: 'rgba(7, 81, 207, 0.1)',
  }
});

export const ButtonsContainer = styled('div', {
  display: 'flex',
  gap: '8px',
});

export const ActionButton = styled('button', {
  flex: 1,
  padding: '6px',
  borderRadius: '4px',
  border: '1px solid transparent',
  fontWeight: 'bold',
  fontSize: '12px',
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  fontFamily: 'Zed, sans-serif',
  position: 'relative',

  '&:hover': {
    animation: `${twistAnimation} 0.5s ease`,
  },

  '&:disabled': {
    cursor: 'not-allowed',
    opacity: 0.7,
  },

  variants: {
    action: {
      buy: {
        backgroundColor: 'rgba(3, 167, 145, 0.2)',
        color: '#03A791',
        borderColor: 'rgba(3, 167, 145, 0.3)',

        '&:hover': {
          backgroundColor: 'rgba(3, 167, 145, 0.3)',
        }
      },
      sell: {
        backgroundColor: 'rgba(231, 76, 60, 0.2)',
        color: '#e74c3c',
        borderColor: 'rgba(231, 76, 60, 0.3)',

        '&:hover': {
          backgroundColor: 'rgba(231, 76, 60, 0.3)',
        }
      },
      reverse: {
        backgroundColor: 'rgba(7, 81, 207, 0)',
        color: 'rgb(7, 81, 207)',
        borderColor: 'rgba(7, 81, 207, 0.3)',

        '&:hover': {
          backgroundColor: 'rgba(7, 81, 207, 0.1)',
        }
      },
      close: {
        backgroundColor: 'rgba(7, 81, 207, 0.1)',
        color: 'rgb(7, 81, 207)',
        borderColor: 'rgba(7, 81, 207, 0.2)',
        padding: '2px 4px',
        fontSize: '9px',
        minWidth: '40px',
        flex: '0 0 auto',

        '&:hover': {
          backgroundColor: 'rgba(7, 81, 207, 0.2)',
        }
      }
    },
    loading: {
      true: {
        animation: `${pulseAnimation} 1s ease infinite`,
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: '0',
          left: '0',
          height: '2px',
          backgroundColor: 'currentColor',
          width: '0%',
          opacity: 0.5,
          animation: `${progressAnimation} 1.5s linear infinite`,
        },
        '&:hover': {
          animation: `${pulseAnimation} 1s ease infinite`,
        }
      },
      false: {
        animation: 'none',
        '&::after': {
          content: 'none',
          animation: 'none',
        }
      }
    }
  }
});

// Position drawer styles
const slideDown = keyframes({
  '0%': { opacity: 0, transform: 'translateY(-20px)', maxHeight: '0' },
  '100%': { opacity: 1, transform: 'translateY(0)', maxHeight: '150px' }
});

const slideUp = keyframes({
  '0%': { opacity: 1, transform: 'translateY(0)', maxHeight: '150px' },
  '100%': { opacity: 0, transform: 'translateY(-20px)', maxHeight: '0' }
});

export const PositionsContainer = styled('div', {
  width: '94%', // Slightly narrower than the panel
  backgroundColor: 'rgb(242, 246, 253)',
  borderRadius: '0 0 4px 4px',
  border: '1px solid rgba(7, 81, 207, 0.3)',
  borderTop: 'none',
  overflow: 'hidden',
  zIndex: 200,
  marginTop: '-1px', // Overlap the bottom border of the panel by 1px
  alignSelf: 'center', // Center it within the parent container
  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.08)', // Subtle shadow for the positions container

  variants: {
    open: {
      true: {
        animation: `${slideDown} 0.3s ease forwards`,
        display: 'block',
      },
      false: {
        animation: `${slideUp} 0.3s ease forwards`,
        display: 'none',
      }
    }
  },

  defaultVariants: {
    open: false
  }
});

export const PositionsList = styled('div', {
  display: 'flex',
  flexDirection: 'column',
  gap: '4px',
  maxHeight: '120px',
  overflowY: 'auto',
  padding: '6px',

  '&::-webkit-scrollbar': {
    width: '3px',
  },
  '&::-webkit-scrollbar-track': {
    background: 'rgba(7, 81, 207, 0.05)',
  },
  '&::-webkit-scrollbar-thumb': {
    background: 'rgba(7, 81, 207, 0.2)',
    borderRadius: '2px',
  }
});

export const PositionItem = styled('div', {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '6px',
  borderRadius: '4px',
  fontSize: '11px',
  backgroundColor: 'rgba(255, 255, 255, 0.5)',
  border: '1px solid rgba(7, 81, 207, 0.1)',
  gap: '8px',

  variants: {
    side: {
      buy: {
        borderLeft: '3px solid #03A791',
      },
      sell: {
        borderLeft: '3px solid #e74c3c',
      }
    }
  }
});

export const PositionDetails = styled('div', {
  display: 'flex',
  flexDirection: 'column',
  gap: '2px',
  flex: '1 1 auto',
  minWidth: 0, // Allow text to truncate if needed
});

export const PositionSymbol = styled('div', {
  fontWeight: 'bold',
  color: 'rgb(7, 81, 207)',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: 'flex',
  alignItems: 'center',
  gap: '4px',
});

export const PositionAmount = styled('div', {
  fontSize: '10px',
  color: 'rgba(7, 81, 207, 0.7)',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: 'flex',
  alignItems: 'center',
  gap: '4px',
});

export const PositionSide = styled('span', {
  fontWeight: 'bold',
  fontSize: '9px',
  padding: '1px 3px',
  borderRadius: '2px',

  variants: {
    side: {
      long: {
        backgroundColor: 'rgba(3, 167, 145, 0.15)',
        color: '#03A791',
      },
      short: {
        backgroundColor: 'rgba(231, 76, 60, 0.15)',
        color: '#e74c3c',
      }
    }
  }
});

export const PositionPnl = styled('div', {
  fontSize: '10px',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: 'flex',
  alignItems: 'center',
  gap: '4px',

  variants: {
    status: {
      positive: {
        // backgroundColor: 'rgba(3, 167, 145, 0.15)',
        color: '#03A791',
      },
      negative: {
        // backgroundColor: 'rgba(231, 76, 60, 0.15)',
        color: '#e74c3c',
      }
    }
  }
});

export const EmptyPositions = styled('div', {
  padding: '8px',
  textAlign: 'center',
  color: 'rgba(7, 81, 207, 0.5)',
  fontStyle: 'italic',
  fontSize: '11px',
});
