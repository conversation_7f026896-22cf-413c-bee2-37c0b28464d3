import { styled, keyframes } from '@stitches/react';

const slideIn = keyframes({
  '0%': { opacity: 0, transform: 'translateY(-20px)' },
  '100%': { opacity: 1, transform: 'translateY(0)' }
});

const slideOut = keyframes({
  '0%': { opacity: 1, transform: 'translateY(0)' },
  '100%': { opacity: 0, transform: 'translateY(-20px)' }
});

export const NotificationContainer = styled('div', {
  position: 'fixed',
  top: '20px',
  right: '20px',
  zIndex: 10000,
  display: 'flex',
  flexDirection: 'column',
  gap: '10px',
  alignItems: 'flex-end',
});

export const NotificationItem = styled('div', {
  backgroundColor: 'rgba(255, 255, 255, 0.95)',
  borderRadius: '4px',
  border: '1px solid rgba(7, 81, 207, 0.3)',
  padding: '12px 16px',
  minWidth: '250px',
  maxWidth: '350px',
  backdropFilter: 'blur(5px)',
  boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
  fontWeight: 'bold',
  fontSize: '14px',
  animation: `${slideIn} 0.3s ease forwards`,
  userSelect: 'none',

  '&.exiting': {
    animation: `${slideOut} 0.3s ease forwards`,
  },

  variants: {
    type: {
      success: {
        color: 'rgb(7, 81, 207)',
      },
      error: {
        color: '#e74c3c',
      }
    }
  },

  defaultVariants: {
    type: 'success'
  }
});

export const NotificationIcon = styled('div', {
  fontSize: '18px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
});

export const NotificationContent = styled('div', {
  flex: 1,
});

export const NotificationClose = styled('div', {
  cursor: 'pointer',
  fontSize: '14px',
  opacity: 0.7,
  transition: 'opacity 0.2s ease',

  '&:hover': {
    opacity: 1,
  },
});
