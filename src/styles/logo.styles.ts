import { styled, keyframes } from '@stitches/react';

// Define the wave animation
const waveAnimation = keyframes({
  '0%, 100%': { transform: 'translateY(0)' },
  '50%': { transform: 'translateY(-3px)' }
});

// Styled component for individual characters
export const Logo<PERSON>har = styled('span', {
  display: 'inline-block',
  animation: `${waveAnimation} 2.5s ease-in-out infinite`,

  // Variants for different animation delays
  variants: {
    position: {
      0: { animationDelay: '0s' },
      1: { animationDelay: '0.15s' },
      2: { animationDelay: '0.3s' },
      3: { animationDelay: '0.45s' },
      4: { animationDelay: '0.6s' },
      5: { animationDelay: '0.75s' },
      6: { animationDelay: '0.9s' },
      7: { animationDelay: '1.05s' }
    }
  }
});

export const LogoContainer = styled('div', {
  position: 'relative',
  marginRight: '30px',
  marginLeft: '15px',
  width: '120px',
  height: '36px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  pointerEvents: 'none',
  userSelect: 'none',
});

export const LogoText = styled('div', {
  color: 'rgb(7, 81, 207)',
  fontSize: '20px',
  fontWeight: 'bold',
  fontFamily: 'Zed, sans-serif',
  position: 'relative',
  zIndex: 5,
  textAlign: 'center',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  letterSpacing: '1px', // Add a small letter spacing for better wave visibility
});

// Concentric rectangles
export const ConcentricRect = styled('div', {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  border: '1px solid rgb(7, 81, 207)',

  variants: {
    level: {
      1: {
        width: '100%',
        height: '100%',
        opacity: 0.8,
        borderRadius: '2px',
      },
      2: {
        width: '85%',
        height: '85%',
        opacity: 0.6,
        borderRadius: '2px',
      },
      3: {
        width: '70%',
        height: '70%',
        opacity: 0.4,
        borderRadius: '1px',
      },
      4: {
        width: '55%',
        height: '55%',
        opacity: 0.2,
        borderRadius: '1px',
      }
    }
  }
});
