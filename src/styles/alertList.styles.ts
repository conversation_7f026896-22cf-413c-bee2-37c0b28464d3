import { styled, keyframes } from '@stitches/react';
import { YAXIS_WIDTH } from '../common/constants';

const fadeIn = keyframes({
  '0%': { opacity: 0 },
  '100%': { opacity: 1 }
});

export const AlertListContainer = styled('div', {
  position: 'absolute',
  top: '20%',
  right: `${YAXIS_WIDTH + 10}px`, // Position exactly at the edge of yAxis
  maxHeight: 'calc(100% - 150px)',
  overflowY: 'auto',
  backgroundColor: 'transparent',
  zIndex: 1000,
  animation: `${fadeIn} 0.3s ease forwards`,

  // Hide scrollbar but keep functionality
  '&::-webkit-scrollbar': {
    display: 'none',
  },
  scrollbarWidth: 'none',
  msOverflowStyle: 'none',
});

export const AlertListItem = styled('div', {
  padding: '8px 12px',
  marginBottom: '8px',
  fontSize: '14px',
  color: 'rgb(7, 81, 207)',
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  borderRadius: '4px',
  position: 'relative',
  textAlign: 'right', // End-align text
  direction: 'rtl', // Allow content to extend to the left
  fontStyle: 'italic',


  variants: {
    crossed: {
      true: {
        '&::after': {
          content: '""',
          position: 'absolute',
          top: '50%',
          left: '0',
          right: '0',
          height: '2px',
          backgroundColor: 'rgb(7, 81, 207)',
          opacity: 0.7,
          // Make it look like a pencil scratch
          backgroundImage: 'linear-gradient(270deg, rgb(7, 81, 207) 70%, transparent 30%)', // Changed to 270deg for RTL
          backgroundSize: '6px 1px',
        }
      }
    }
  }
});

export const AlertListEmpty = styled('div', {
  padding: '10px',
  fontSize: '14px',
  color: 'rgba(7, 81, 207, 0.7)',
  textAlign: 'right', // End-align empty message to match other elements
  fontStyle: 'italic',
});
