import { styled } from '@stitches/react';

export const ContextMenuContainer = styled('div', {
  position: 'absolute', // Use absolute positioning to position relative to the chart container
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: 'rgba(255, 255, 255, 0.95)',
  borderRadius: '4px',
  border: '1px solid rgba(7, 81, 207, 0.3)',
  zIndex: 9999, // Very high z-index to ensure it's on top of everything
  overflow: 'hidden',
  minWidth: '200px',
  backdropFilter: 'blur(5px)',
  userSelect: 'none',
});

export const ContextMenuItem = styled('div', {
  padding: '10px 14px',
  fontSize: '14px',
  color: 'rgb(7, 81, 207)',
  cursor: 'pointer',
  transition: 'all 0.1s ease',
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  fontWeight: 'bold',

  '&:hover': {
    backgroundColor: 'rgba(7, 81, 207, 0.15)',
  },

  '&:active': {
    backgroundColor: 'rgba(7, 81, 207, 0.25)',
  },
});

export const ContextMenuDivider = styled('div', {
  height: '1px',
  backgroundColor: 'rgba(7, 81, 207, 0.2)',
  margin: '2px 0',
});
