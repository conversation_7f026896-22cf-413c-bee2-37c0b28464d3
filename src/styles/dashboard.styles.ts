import { styled } from '@stitches/react';

export const SnapshotButton = styled('div', {
  position: 'fixed',
  bottom: '80px',
  right: '150px',
  width: '40px',
  height: '40px',
  borderRadius: '50%',
  backgroundColor: 'rgba(255, 255, 255, 0.95)',
  border: '1px solid rgba(7, 81, 207, 0.3)',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  zIndex: 1000,
  backdropFilter: 'blur(5px)',
  transition: 'all 0.2s ease',
  color: 'rgb(7, 81, 207)',

  '&:hover': {
    transform: 'scale(1.05)',
    boxShadow: '0 4px 15px rgba(0, 0, 0, 0.15)',
  },

  '&:active': {
    transform: 'scale(0.95)',
  },
});