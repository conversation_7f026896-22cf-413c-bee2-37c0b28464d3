import { styled } from "@stitches/react";

export const ChartContainer = styled('div', {
    display: 'flex',
    flexDirection: 'row',
    position: 'relative',
    width: '100%',
    height: '100%',
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',
});

export const PaneContainer = styled('canvas', {
    display: 'block',
    backgroundColor: 'transparent',
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',
});

export const YAxisContainer = styled('div', {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    position: 'relative',
    height: '100%',
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',
});

export const YAxisLabel = styled('div', {
    position: 'absolute',
    right: '5px',
    transform: 'translateY(-50%)',
    fontSize: '10px',
    color: '#666',
    whiteSpace: 'nowrap',
});
