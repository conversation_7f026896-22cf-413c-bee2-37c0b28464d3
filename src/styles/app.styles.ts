import { styled, globalCss } from '@stitches/react'
import { XAXIS_HEIGHT, YAXIS_WIDTH } from '../common/constants';

// Global styles to prevent selection and blue highlight effect
export const globalStyles = globalCss({
  // Prevent blue highlight on all elements
  '*': {
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',
    outline: 'none',
  },

  // Prevent text selection on the entire app
  'body, html, #root': {
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
  },

  // Specific styles for Konva elements
  '.konvajs-content': {
    userSelect: 'none !important',
    WebkitUserSelect: 'none !important',
    MozUserSelect: 'none !important',
    msUserSelect: 'none !important',
    WebkitTapHighlightColor: 'rgba(0,0,0,0) !important',
    outline: 'none !important',

    '& canvas': {
      userSelect: 'none !important',
      WebkitUserSelect: 'none !important',
      MozUserSelect: 'none !important',
      msUserSelect: 'none !important',
      WebkitTapHighlightColor: 'rgba(0,0,0,0) !important',
      outline: 'none !important',
    }
  },

  // Prevent blue highlight on draggable elements
  '[draggable="true"]': {
    WebkitUserDrag: 'none',
    userDrag: 'none',
    WebkitTapHighlightColor: 'rgba(0,0,0,0)',
  }
});


export const Container = styled('div', {
  position: 'relative',
  width: '100%',
  height: '100%',
  // overflow: 'hidden',
  userSelect: 'none',
  WebkitUserSelect: 'none',
  MozUserSelect: 'none',
  msUserSelect: 'none',
  WebkitTapHighlightColor: 'rgba(0,0,0,0)',
})

export const Header = styled('div', {
  position: 'absolute',
  left: 0,
  top: 0,
  width: '100%',
  display: 'flex',
  pointerEvents: 'none',
  flexDirection: 'row',
  paddingBottom: '5px',
  paddingTop: '5px',
  alignItems: 'center',
  borderBottom: '1px dotted transparent',
  backgroundImage: 'linear-gradient(90deg, rgba(7, 81, 207, 0.7), rgba(7, 81, 207, 0.7) 20%, transparent 40%)',
  backgroundSize: '100% 1px',
  backgroundPosition: 'bottom',
  backgroundRepeat: 'no-repeat'
})

export const Footer = styled('div', {
  position: 'absolute',
  left: 0,
  bottom: 0,
  height: XAXIS_HEIGHT,
  width: '100%',
  display: 'flex',
  flexDirection: 'row',
  userSelect: 'none',
  WebkitUserSelect: 'none',
  MozUserSelect: 'none',
  msUserSelect: 'none',
  WebkitTapHighlightColor: 'rgba(0,0,0,0)',
})

export const PaperBackground = styled('div', {
  position: 'absolute',
  top: 0,
  left: 0,
  width: '100%',
  height: '100%',
  backgroundColor: '#FEF3E2',
  backgroundImage: 'url("../src/asset/noise.png")',
  backgroundRepeat: 'repeat',
  backgroundSize: '0.3 0.3',
  opacity: 0.03,
})

export const BlueprintGrid = styled('div', {
  position: 'absolute',
  top: 0,
  left: 0,
  width: `calc(100% - ${YAXIS_WIDTH}px)`,
  height: `calc(100% - ${XAXIS_HEIGHT}px)`,
  backgroundSize: '20px 20px',
  backgroundImage: `
    linear-gradient(to right, rgba(30, 144, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(30, 144, 255, 0.05) 1px, transparent 1px)
  `,
  userSelect: 'none',
  WebkitUserSelect: 'none',
  MozUserSelect: 'none',
  msUserSelect: 'none',
  WebkitTapHighlightColor: 'rgba(0,0,0,0)',
  '&::after': {
    content: '""',
    position: 'absolute',
    left: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
    background: 'linear-gradient(to top,rgba(30, 144, 255, 0.03), transparent)',
    pointerEvents: 'none',
  },
})


export const Watermark = styled('img', {
  position: 'absolute',
  top: '60%',
  left: '70%',

  transform: 'translate(-50%, -50%)',
  // filter: 'hue-rotate(30deg) contrast(100%)',
  opacity: 0.04,
  pointerEvents: 'none',
  zIndex: 0,
  objectFit: 'contain',
  maskImage: 'url("../src/asset/circles-mask.svg")',
  maskSize: '35%', // Reduced from 'contain' to make image overflow the mask
  maskRepeat: 'no-repeat',
  maskPosition: 'center center',

  '@media (min-width: 600px)': {
    width: '80%',
    height: '80%',
    maskSize: '30%',
    left: '70%',
  },

  '@media (max-width: 600px)': {
    width: '100%',
    height: '100%',
    maskSize: '50%',
    left: '50%',
  },
})
