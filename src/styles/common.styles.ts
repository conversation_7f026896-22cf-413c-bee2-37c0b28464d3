import { css } from '@stitches/react';

export const LiquidGlass = css({
    background: 'rgba(7, 80, 207, 0.02)',
    backdropFilter: 'blur(2px)',
    borderRadius: 'inherit',
    border: '1px solid rgba(7, 80, 207, 0.06)',
    boxShadow: '1px 2px 10px 1px rgba(0, 0, 0, 0.05)',
    transition: 'all 0.3s ease-in-out',

    '&::after': {
        borderRadius: 'inherit',
        content: '',
        position: 'absolute',
        inset: 0,
        padding: '1px',
        background: `
            linear-gradient(to top right, transparent 40%, rgba(255, 255, 255, 0.69) 50%, transparent 80%) top left,
            linear-gradient(to bottom left, transparent 40%, rgba(255, 255, 255, 0.2) 50%, transparent 80%) top right,
            linear-gradient(to top right, transparent 40%, rgba(255, 255, 255, 0.2) 50%, transparent 80%) bottom right,
            linear-gradient(to bottom left, transparent 40%, rgba(255, 255, 255, 0.2) 50%, transparent 80%) bottom left
        `,
        backgroundRepeat: 'no-repeat',
        WebkitMask:
            'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
        WebkitMaskComposite: 'xor',
        maskComposite: 'exclude',
        pointerEvents: 'none',
        transition: 'opacity 0.3s ease-in-out'
    },

})