import { Shape } from "react-konva";
import { useCallback, useContext, useRef } from "react";
import Konva from "konva";
import { HistogramPlotData } from "../storage/histogram.pd";
import { useScale } from "../hooks/useScale";
import { AppContext } from "../contexts/app.context";
import { usePlotDrawing } from "../hooks/usePlotDrawing";

export function HistogramPlot(props: { plotData: HistogramPlotData }) {
    const { deltaT2x, t2x, v2y } = useScale()
    const { timeRange, timeUnit, tick } = useContext(AppContext)
    const shapeRef = useRef<Konva.Shape>(null);
    const { drawPriceLine, shouldShowPriceLine, getLastValue, getDataPointColor } = usePlotDrawing();

    const sceneFunc = useCallback((context: Konva.Context) => {
        const list = props.plotData.selectByTime(timeRange.min, timeRange.max)
        let barWidth = Math.max(deltaT2x(timeUnit) - (props.plotData.config?.barSpacing || 3), 1);

        // Draw histogram bars
        for (const hist of list) {
            if (hist.value === null || hist.value === undefined) continue;

            const x = t2x(hist.time);
            const y = v2y(hist.value);
            const baselineY = v2y(0); // Assuming 0 is the baseline
            const color = getDataPointColor(hist, props.plotData.config);

            context.beginPath();
            context.rect(x - barWidth / 2, Math.min(y, baselineY), barWidth, Math.abs(y - baselineY));
            context.setAttr('fillStyle', color);
            context.fill();
        }

        // Draw price line (last value line) if configured
        if (shouldShowPriceLine(props.plotData.config, tick)) {
            const last = props.plotData.last;
            const lastValue = getLastValue(last);

            if (lastValue !== undefined) {
                const color = getDataPointColor(last, props.plotData.config);
                const priceLineY = v2y(lastValue);

                // Draw price line with shadow effect
                drawPriceLine(context, priceLineY, t2x(timeRange.max), color);
            }
        }
    }, [props.plotData, timeRange, t2x, v2y, tick, timeUnit, deltaT2x])

    return (
        <Shape
            ref={shapeRef}
            sceneFunc={sceneFunc}
        />
    )
}
