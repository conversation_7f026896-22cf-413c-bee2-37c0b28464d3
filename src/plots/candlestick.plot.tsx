import { Shape } from "react-konva";
import { useCallback, useContext } from "react";
import Konva from "konva";
import { type CandlestickPlotData } from "../storage/candlestick.pd";
import { useScale } from "../hooks/useScale";
import { AppContext } from "../contexts/app.context";
import { usePlotDrawing } from "../hooks/usePlotDrawing";

export function CandlestickPlot(props: { plotData: CandlestickPlotData }) {
    const { plotData } = props
    const { deltaT2x, v2y, t2x } = useScale()
    const { timeRange, timeUnit, tick } = useContext(AppContext)
    const { drawPriceLine, shouldShowPriceLine, getLastValue, getDataPointColor } = usePlotDrawing()
    function addBullBarEffect(context: Konva.Context, x: number, y: number, width: number, height: number, color: string) {
        // Bull bars: hollow body with thick outline and inner body with margin
        context.save();

        // Add thick outline (no fill for hollow effect)
        context.strokeStyle = color;
        context.lineWidth = 2;
        context.strokeRect(x, y, width, height);

        // Add inner body with dynamic margin based on size
        if (width > 8 && height > 8) { // Only add inner body if outer body is large enough
            // Dynamic margin: scales with body size but has min/max bounds
            const marginWidth = Math.max(1, Math.min(width * 0.2, width / 4));
            const marginHeight = Math.max(1, Math.min(height * 0.2, height / 4));

            const innerX = x + marginWidth;
            const innerY = y + marginHeight;
            const innerWidth = width - (marginWidth * 2);
            const innerHeight = height - (marginHeight * 2);

            // Only draw inner body if it's still visible after margins
            if (innerWidth > 2 && innerHeight > 2) {
                context.strokeStyle = color;
                context.lineWidth = 1;
                context.strokeRect(innerX, innerY, innerWidth, innerHeight);
            }
        }

        context.restore();
    }

    function addBearBarEffect(context: Konva.Context, x: number, y: number, width: number, height: number, color: string) {
        // Bear bars: thin outline with hollow body
        context.save();

        // Draw thin outline only (no fill for hollow effect)
        context.strokeStyle = color;
        context.lineWidth = 1;
        context.strokeRect(x, y, width, height);

        context.restore();
    }

    const sceneFunc = useCallback((context: Konva.Context) => {
        const list = plotData.selectByTime(timeRange.min, timeRange.max)
        let barWidth = deltaT2x(timeUnit) - (plotData.config?.barSpacing || 3);

        barWidth = barWidth <= 1 ? 1 : barWidth;
        const cornerRadius = Math.min(4, barWidth / 4);
        const minHeightForRoundCorners = cornerRadius * 3;

        for (const candle of list) {
            const closeY = v2y(candle.close);
            const openY = v2y(candle.open);
            const x = t2x(candle.time);
            const highY = v2y(candle.high);
            const lowY = v2y(candle.low);
            const change = candle.close - candle.open
            const color = candle.color || (change > 0 ? plotData.config?.riseColor : plotData.config?.fallColor)
            const fill = color

            if (barWidth <= 2) {
                // Simple line for very thin bars
                context.beginPath();
                context.moveTo(x, highY);
                context.lineTo(x, lowY);
                context.closePath();
                context.setAttr('strokeStyle', fill);
                context.setAttr('lineWidth', barWidth);
                context.stroke();
            } else {
                // Draw wicks as straight lines
                const wickWidth = Math.min(barWidth, 2);
                context.setAttr('strokeStyle', fill);
                context.setAttr('lineWidth', wickWidth);

                // Upper wick - straight line
                if (highY < Math.min(closeY, openY)) {
                    context.beginPath();
                    context.moveTo(x, highY);
                    context.lineTo(x, Math.min(closeY, openY));
                    context.stroke();
                }

                // Lower wick - straight line
                if (lowY > Math.max(closeY, openY)) {
                    context.beginPath();
                    context.moveTo(x, Math.max(closeY, openY));
                    context.lineTo(x, lowY);
                    context.stroke();
                }

                // Draw body
                const bodyX = x - barWidth / 2;
                const bodyY = Math.min(closeY, openY);
                const bodyHeight = Math.max(Math.abs(closeY - openY), 1);

                // Determine if this is a bull (rising) or bear (falling) bar
                const isBullBar = change > 0;

                if (barWidth > 4) {
                    // Apply different effects based on bull/bear
                    if (isBullBar) {
                        addBullBarEffect(context, bodyX, bodyY, barWidth, bodyHeight, fill);
                    } else {
                        addBearBarEffect(context, bodyX, bodyY, barWidth, bodyHeight, fill);
                    }
                } else {
                    // For smaller bars, just draw simple filled rectangle
                    context.beginPath();
                    if (bodyHeight < minHeightForRoundCorners) {
                        context.rect(bodyX, bodyY, barWidth, bodyHeight);
                    } else {
                        context.moveTo(bodyX + cornerRadius, bodyY);
                        context.lineTo(bodyX + barWidth - cornerRadius, bodyY);
                        context.arcTo(bodyX + barWidth, bodyY, bodyX + barWidth, bodyY + cornerRadius, cornerRadius);
                        context.lineTo(bodyX + barWidth, bodyY + bodyHeight - cornerRadius);
                        context.arcTo(bodyX + barWidth, bodyY + bodyHeight, bodyX + barWidth - cornerRadius, bodyY + bodyHeight, cornerRadius);
                        context.lineTo(bodyX + cornerRadius, bodyY + bodyHeight);
                        context.arcTo(bodyX, bodyY + bodyHeight, bodyX, bodyY + bodyHeight - cornerRadius, cornerRadius);
                        context.lineTo(bodyX, bodyY + cornerRadius);
                        context.arcTo(bodyX, bodyY, bodyX + cornerRadius, bodyY, cornerRadius);
                    }

                    context.setAttr('fillStyle', fill);
                    context.setAttr('globalAlpha', 0.9);
                    context.fill();
                    context.setAttr('globalAlpha', 1);
                }
            }
        }

        // Draw price line
        if (shouldShowPriceLine(plotData.config, tick)) {
            const last = plotData.last;
            const lastValue = getLastValue(last);

            if (lastValue !== undefined) {
                const color = getDataPointColor(last, plotData.config);
                const priceLineY = v2y(lastValue);

                // Draw price line with shadow effect
                drawPriceLine(context, priceLineY, t2x(timeRange.max), color);
            }
        }
    }, [plotData, timeRange, v2y, t2x, tick])

    return (
        <Shape
            sceneFunc={sceneFunc}
        />
    )
}
