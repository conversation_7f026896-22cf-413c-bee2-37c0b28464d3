import { Shape } from "react-konva";
import { useCallback, useContext } from "react";
import Konva from "konva";
import { type CandlestickPlotData } from "../storage/candlestick.pd";
import { useScale } from "../hooks/useScale";
import { AppContext } from "../contexts/app.context";
import { usePlotDrawing } from "../hooks/usePlotDrawing";

export function CandlestickPlot(props: { plotData: CandlestickPlotData }) {
    const { plotData } = props
    const { deltaT2x, v2y, t2x } = useScale()
    const { timeRange, timeUnit, tick } = useContext(AppContext)
    const { drawPriceLine, shouldShowPriceLine, getLastValue, getDataPointColor } = usePlotDrawing()
    function addBullBarEffect(context: Konva.Context, x: number, y: number, width: number, height: number, color: string) {
        // Bull bars: thick outline with parallel stripes across body
        context.save();

        // Fill the body with base color
        context.fillStyle = color;
        context.fillRect(x, y, width, height);

        // Add thick outline
        context.strokeStyle = color;
        context.lineWidth = 2;
        context.strokeRect(x, y, width, height);

        // Add parallel stripes across the body
        if (height > 6) { // Only add stripes if bar is tall enough
            context.strokeStyle = color;
            context.lineWidth = 1;
            context.globalAlpha = 0.6;

            const stripeSpacing = Math.max(3, height / 6); // Adjust spacing based on height
            for (let i = y + stripeSpacing; i < y + height - 1; i += stripeSpacing) {
                context.beginPath();
                context.moveTo(x + 1, i);
                context.lineTo(x + width - 1, i);
                context.stroke();
            }
        }

        context.restore();
    }

    function addBearBarEffect(context: Konva.Context, x: number, y: number, width: number, height: number, color: string) {
        // Bear bars: thin outline with hollow body
        context.save();

        // Draw thin outline only (no fill for hollow effect)
        context.strokeStyle = color;
        context.lineWidth = 1;
        context.strokeRect(x, y, width, height);

        context.restore();
    }

    const sceneFunc = useCallback((context: Konva.Context) => {
        const list = plotData.selectByTime(timeRange.min, timeRange.max)
        let barWidth = deltaT2x(timeUnit) - (plotData.config?.barSpacing || 3);

        barWidth = barWidth <= 1 ? 1 : barWidth;
        const cornerRadius = Math.min(4, barWidth / 4);
        const minHeightForRoundCorners = cornerRadius * 3;

        for (const candle of list) {
            const closeY = v2y(candle.close);
            const openY = v2y(candle.open);
            const x = t2x(candle.time);
            const highY = v2y(candle.high);
            const lowY = v2y(candle.low);
            const change = candle.close - candle.open
            const color = candle.color || (change > 0 ? plotData.config?.riseColor : plotData.config?.fallColor)
            const fill = color

            if (barWidth <= 2) {
                // Simple line for very thin bars
                context.beginPath();
                context.moveTo(x, highY);
                context.lineTo(x, lowY);
                context.closePath();
                context.setAttr('strokeStyle', fill);
                context.setAttr('lineWidth', barWidth);
                context.stroke();
            } else {
                // Draw wicks with sketchy effect
                const wickWidth = Math.min(barWidth, 2);
                context.setAttr('strokeStyle', fill);
                context.setAttr('lineWidth', wickWidth);

                // Upper wick with slight randomness
                context.beginPath();
                let currentY = highY;
                while (currentY < Math.min(closeY, openY)) {
                    const nextY = Math.min(currentY + 2, Math.min(closeY, openY));
                    const offsetX = (Math.random() - 0.5) * 0.5;
                    context.moveTo(x + offsetX, currentY);
                    context.lineTo(x + offsetX, nextY);
                    currentY = nextY;
                }
                context.stroke();

                // Lower wick with slight randomness
                context.beginPath();
                currentY = Math.max(closeY, openY);
                while (currentY < lowY) {
                    const nextY = Math.min(currentY + 2, lowY);
                    const offsetX = (Math.random() - 0.5) * 0.5;
                    context.moveTo(x + offsetX, currentY);
                    context.lineTo(x + offsetX, nextY);
                    currentY = nextY;
                }
                context.stroke();

                // Draw body
                const bodyX = x - barWidth / 2;
                const bodyY = Math.min(closeY, openY);
                const bodyHeight = Math.max(Math.abs(closeY - openY), 1);

                // Determine if this is a bull (rising) or bear (falling) bar
                const isBullBar = change > 0;

                if (barWidth > 4) {
                    // Apply different effects based on bull/bear
                    if (isBullBar) {
                        addBullBarEffect(context, bodyX, bodyY, barWidth, bodyHeight, fill);
                    } else {
                        addBearBarEffect(context, bodyX, bodyY, barWidth, bodyHeight, fill);
                    }
                } else {
                    // For smaller bars, just draw simple filled rectangle
                    context.beginPath();
                    if (bodyHeight < minHeightForRoundCorners) {
                        context.rect(bodyX, bodyY, barWidth, bodyHeight);
                    } else {
                        context.moveTo(bodyX + cornerRadius, bodyY);
                        context.lineTo(bodyX + barWidth - cornerRadius, bodyY);
                        context.arcTo(bodyX + barWidth, bodyY, bodyX + barWidth, bodyY + cornerRadius, cornerRadius);
                        context.lineTo(bodyX + barWidth, bodyY + bodyHeight - cornerRadius);
                        context.arcTo(bodyX + barWidth, bodyY + bodyHeight, bodyX + barWidth - cornerRadius, bodyY + bodyHeight, cornerRadius);
                        context.lineTo(bodyX + cornerRadius, bodyY + bodyHeight);
                        context.arcTo(bodyX, bodyY + bodyHeight, bodyX, bodyY + bodyHeight - cornerRadius, cornerRadius);
                        context.lineTo(bodyX, bodyY + cornerRadius);
                        context.arcTo(bodyX, bodyY, bodyX + cornerRadius, bodyY, cornerRadius);
                    }

                    context.setAttr('fillStyle', fill);
                    context.setAttr('globalAlpha', 0.9);
                    context.fill();
                    context.setAttr('globalAlpha', 1);
                }
            }
        }

        // Draw price line
        if (shouldShowPriceLine(plotData.config, tick)) {
            const last = plotData.last;
            const lastValue = getLastValue(last);

            if (lastValue !== undefined) {
                const color = getDataPointColor(last, plotData.config);
                const priceLineY = v2y(lastValue);

                // Draw price line with shadow effect
                drawPriceLine(context, priceLineY, t2x(timeRange.max), color);
            }
        }
    }, [plotData, timeRange, v2y, t2x, tick])

    return (
        <Shape
            sceneFunc={sceneFunc}
        />
    )
}
