import { Shape } from "react-konva";
import { use<PERSON><PERSON>back, useContext } from "react";
import Konva from "konva";
import { LinePlotData } from "../storage/line.pd";
import { useScale } from "../hooks/useScale";
import { AppContext } from "../contexts/app.context";
import { TDataPoint } from "../types";
import { isNil } from 'es-toolkit';
import { usePlotDrawing } from "../hooks/usePlotDrawing";

interface ColorSegment {
    startIndex: number;
    endIndex: number;
    color: string;
}

export function LinePlot(props: { plotData: LinePlotData }) {
    const { plotData } = props;
    const { t2x, v2y } = useScale();
    const { timeRange, tick } = useContext(AppContext);
    const { drawPriceLine, drawSimpleLine, shouldShowPriceLine, getLastValue, getDataPointColor } = usePlotDrawing();

    // We're using the reusable drawSimpleLine function from drawingUtils

    // Function to identify color segments in the data
    const identifyColorSegments = (data: TDataPoint[]): ColorSegment[] => {
        if (data.length === 0) return [];

        const segments: ColorSegment[] = [];
        let currentColor = data[0].color || plotData.config?.defaultColor || '#E8EFCF';
        let startIndex = 0;

        for (let i = 1; i < data.length; i++) {
            const pointColor = data[i].color || plotData.config?.defaultColor || '#E8EFCF';

            // If color changes or we hit a line break (null/undefined value), end the current segment
            if (pointColor !== currentColor || isNil(data[i].value)) {
                // Only add segment if it has valid values
                if (!isNil(data[startIndex].value)) {
                    segments.push({
                        startIndex,
                        endIndex: i - 1,
                        color: currentColor
                    });
                }

                // Start a new segment if this point has a valid value
                if (!isNil(data[i].value)) {
                    startIndex = i;
                    currentColor = pointColor;
                } else {
                    // Skip this point and start a new segment at the next valid point
                    startIndex = i + 1;
                    // We'll set the color when we find the next valid point
                }
            }
        }

        // Add the last segment if it has valid values
        if (startIndex < data.length && !isNil(data[startIndex].value)) {
            segments.push({
                startIndex,
                endIndex: data.length - 1,
                color: currentColor
            });
        }

        return segments;
    };

    const sceneFunc = useCallback((context: Konva.Context) => {
        const list = plotData.selectByTime(timeRange.min, timeRange.max);
        if (list.length === 0) return;

        // Identify color segments
        const colorSegments = identifyColorSegments(list);

        // Draw each color segment
        for (const segment of colorSegments) {
            const path: Array<{ x: number, y: number }> = [];

            // Build the path for this segment
            for (let i = segment.startIndex; i <= segment.endIndex; i++) {
                const point = list[i];
                if (!isNil(point.value)) {
                    path.push({
                        x: t2x(point.time),
                        y: v2y(point.value)
                    });
                }
            }

            // Draw the segment as a simple line
            if (path.length > 1) {
                const lineWidth = plotData.config?.lineWidth || 1;
                drawSimpleLine(context, path, segment.color, lineWidth);
            }
        }

        // Draw price line (last value line) if configured
        if (shouldShowPriceLine(plotData.config, tick)) {
            const last = plotData.last;
            const lastValue = getLastValue(last);

            if (lastValue !== undefined) {
                const color = getDataPointColor(last, plotData.config);
                const priceLineY = v2y(lastValue);

                // Draw price line with shadow effect
                drawPriceLine(context, priceLineY, t2x(timeRange.max), color);
            }
        }
    }, [plotData, timeRange, v2y, t2x, tick]);

    return (
        <Shape
            sceneFunc={sceneFunc}
        />
    );
}
