import { Shape } from "react-konva";
import { use<PERSON><PERSON>back, useContext, useMemo } from "react";
import Konva from "konva";
import { useScale } from "../hooks/useScale";
import { ChartContext } from "../contexts/chart.context";
import { AppContext } from "../contexts/app.context";
import { TRange } from "../types";

// Default color if none is provided
const DEFAULT_BAR_COLOR = "#DBFFCB80";

// Function to generate a random color with transparency
const generateRandomColor = () => {
    const r = Math.floor(Math.random() * 256);
    const g = Math.floor(Math.random() * 256);
    const b = Math.floor(Math.random() * 256);
    return `rgba(${r}, ${g}, ${b}, 0.5)`; // 0.5 alpha for semi-transparency
};

export function TradeDistPlot(props: {
    config?: { color?: string; boxColor?: string };
    timeRange: TRange,
    data: number[]
}) {
    const { t2x, v2y, deltaT2x } = useScale();
    const { chartData } = useContext(ChartContext);
    const { timeRange, timeUnit } = useContext(AppContext);

    // Generate a random color when the component initializes
    // Using useMemo to ensure the color stays consistent during re-renders
    const randomColor = useMemo(() => generateRandomColor(), []);

    const sceneFunc = useCallback((context: Konva.Context) => {
        if (!chartData) return;

        // Check if the trade profile time is within the current time range
        if (props.timeRange.min < timeRange.min || props.timeRange.max > timeRange.max) {
            // Skip rendering if the profile is outside the visible time range
            return;
        }

        const items = props.data
        if (!items || items.length === 0) {
            console.warn(`No items found`);
            return;
        }

        // Check if all items are valid numbers
        if (items.some(item => typeof item !== 'number' || isNaN(item))) {
            console.warn(`contains invalid items`);
            return;
        }

        // Calculate the base x position for the trade profile
        const baseX = t2x(props.timeRange.min);
        const endX = t2x(props.timeRange.max);

        // Find the candlestick data for the selected time
        const plotData = chartData.plotDataList.find(pd => pd.config?.isMain)
        if (!plotData) return;

        // Try to find the candlestick at the exact time
        const valueRange = plotData.getValueRangeByTime(props.timeRange.min, props.timeRange.max)
        if (!valueRange) {
            console.log(`No candlestick found for time ${props.timeRange.min}`);
            return;
        }

        const highY = v2y(valueRange.max);
        const lowY = v2y(valueRange.min);

        // Calculate the height for the bars
        const candleHeight = Math.abs(lowY - highY);

        // Calculate candlestick width
        const candlestickWidth = deltaT2x(timeUnit);

        // Draw the border rect, considering candlestick width
        context.beginPath();
        context.rect(baseX - candlestickWidth / 2, highY, endX - baseX + candlestickWidth, candleHeight);
        context.strokeStyle = randomColor
        context.lineWidth = 1;
        context.stroke();

        const barMaxWidth = Math.max(100, (endX - baseX + candlestickWidth) * 0.5); // 50% of the box width

        // Find the maximum value to normalize bar widths
        const maxValue = Math.max(...items);

        // Make sure we have a valid maximum value
        if (maxValue <= 0 || !isFinite(maxValue)) {
            console.log(`Invalid maxValue: ${maxValue} for profile at time ${props.timeRange.min}`);
            return;
        }

        // Add spacing between bars (20% of total height as spacing)
        const spacingPercentage = 0.2;
        const totalSpacing = candleHeight * spacingPercentage;
        const barSpacing = totalSpacing / (items.length - 1 || 1); // Avoid division by zero
        const barHeight = (candleHeight - totalSpacing) / items.length;

        // Draw each bar based on its index from top to bottom
        items.forEach((value, index) => {
            // Calculate bar width based on the value
            const barWidth = (value / maxValue) * barMaxWidth;

            // Calculate y position based on index (from top to bottom)
            // Add spacing between bars
            const yPosition = highY + (index * (barHeight + barSpacing));

            // Draw bar with shadow effect
            context.save();
            context.shadowColor = 'rgba(0, 0, 0, 0.3)';
            context.shadowBlur = 4;
            context.shadowOffsetX = 2;
            context.shadowOffsetY = 2;

            context.beginPath();
            context.rect(endX + candlestickWidth / 2, yPosition, barWidth, barHeight);
            context.fillStyle = props.config?.color || randomColor || DEFAULT_BAR_COLOR;
            context.fill();

            context.restore();
        });

    }, [props.data, props.timeRange, t2x, v2y, deltaT2x, chartData, timeRange, timeUnit]);

    return (
        <Shape
            sceneFunc={sceneFunc}
        />
    );
}
