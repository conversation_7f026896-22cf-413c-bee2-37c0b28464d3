import { useEffect, useState, useRef, useCallback } from "react"
import { Loader } from "./loader"
import { App } from "./app"
import { Selector } from "./components/selector"
import { fetchSymbolList, fetchTimeframeList } from "./common/api"
import { debounce } from "./common/util"
import { Header } from "./styles/app.styles"
import { LogoContainer, LogoText, LogoChar, ConcentricRect } from "./styles/logo.styles"
import SnapshotIcon from "./icons/snapshot.icon"
import { SnapshotButton } from "./styles/dashboard.styles"
import html2canvas from "html2canvas"

// Parse URL path to extract symbol and timeframe
const parseUrlPath = () => {
    // Get the path without leading slash
    const path = window.location.pathname.substring(1);

    if (!path) return null;

    // Expected format: /symbol-timeframe (e.g., /btc-15m)
    const parts = path.split('-');
    if (parts.length !== 2) return null;

    return {
        symbol: parts[0],
        timeframe: parts[1]
    };
};

// Update URL path when symbol or timeframe changes
const updateUrlPath = (symbol: string, timeframe: string) => {
    const newPath = `/${symbol}-${timeframe}`;

    // Only update if different from current path to avoid unnecessary history entries
    if (window.location.pathname !== newPath) {
        window.history.pushState({}, '', newPath);
    }
};

export const Dashboard = () => {
    const loader = new Loader()

    // Track if URL parameters have been applied
    const urlParamsApplied = useRef(false);

    // Parse URL parameters
    const urlParams = parseUrlPath();

    // Initialize state from URL params first, then localStorage, then defaults
    const [symbol, setSymbol] = useState(() => {
        if (urlParams?.symbol) return urlParams.symbol;
        return localStorage.getItem('symbol') ?? 'eth';
    });

    const [symbolList, setSymbolList] = useState<string[]>([]);
    const [timeframeList, setTimeframeList] = useState<string[]>([]);

    const [timeframe, setTimeframe] = useState(() => {
        if (urlParams?.timeframe) return urlParams.timeframe;
        return localStorage.getItem('timeframe') ?? '3m';
    });

    const [isChangingSymbol, setIsChangingSymbol] = useState(false);
    const [isChangingTimeframe, setIsChangingTimeframe] = useState(false);
    const lastSymbolChangeTime = useRef<number>(0);
    const lastTimeframeChangeTime = useRef<number>(0);

    const dashboardRef = useRef<HTMLDivElement>(null);


    const capturePage = () => {
        if (!dashboardRef.current) return;
        html2canvas(dashboardRef.current).then(canvas => {
            const imgData = canvas.toDataURL('image/png');
            const link = document.createElement('a');
            link.href = imgData;
            link.download = `${symbol}-${timeframe}-screenshot.png`;
            link.click();
        });
    };


    // Effect to fetch symbol and timeframe lists
    useEffect(() => {
        fetchSymbolList().then(data => {
            setSymbolList(data)
        })
        fetchTimeframeList().then(data => {
            setTimeframeList(data)
        })
    }, [])

    // Effect to handle URL parameters on initial load
    useEffect(() => {
        // Only apply URL parameters once on initial load
        if (urlParams && !urlParamsApplied.current && symbolList.length > 0 && timeframeList.length > 0) {
            // Mark as applied to prevent reapplying on subsequent renders
            urlParamsApplied.current = true;

            // Validate symbol and timeframe against available options
            const validSymbol = symbolList.includes(urlParams.symbol) ? urlParams.symbol : symbol;
            const validTimeframe = timeframeList.includes(urlParams.timeframe) ? urlParams.timeframe : timeframe;

            // Only update if the values are different and valid
            if (validSymbol !== symbol || validTimeframe !== timeframe) {
                console.log(`Applying URL parameters: ${validSymbol}-${validTimeframe}`);

                // Update state and localStorage
                if (validSymbol !== symbol) {
                    setSymbol(validSymbol);
                    localStorage.setItem('symbol', validSymbol);
                }

                if (validTimeframe !== timeframe) {
                    setTimeframe(validTimeframe);
                    localStorage.setItem('timeframe', validTimeframe);
                }

                // Update URL if needed (in case we had to correct invalid parameters)
                if (validSymbol !== urlParams.symbol || validTimeframe !== urlParams.timeframe) {
                    updateUrlPath(validSymbol, validTimeframe);
                }
            }
        }
    }, [symbolList, timeframeList, urlParams, symbol, timeframe])

    // Effect to handle browser back/forward navigation
    useEffect(() => {
        const handlePopState = () => {
            const newUrlParams = parseUrlPath();
            if (newUrlParams && symbolList.length > 0 && timeframeList.length > 0) {
                // Validate symbol and timeframe against available options
                const validSymbol = symbolList.includes(newUrlParams.symbol) ? newUrlParams.symbol : symbol;
                const validTimeframe = timeframeList.includes(newUrlParams.timeframe) ? newUrlParams.timeframe : timeframe;

                // Only update if the values are different and valid
                if (validSymbol !== symbol) {
                    console.log(`Updating symbol from popstate: ${validSymbol}`);
                    setSymbol(validSymbol);
                    localStorage.setItem('symbol', validSymbol);
                }

                if (validTimeframe !== timeframe) {
                    console.log(`Updating timeframe from popstate: ${validTimeframe}`);
                    setTimeframe(validTimeframe);
                    localStorage.setItem('timeframe', validTimeframe);
                }

                // Update URL if needed (in case we had to correct invalid parameters)
                if (validSymbol !== newUrlParams.symbol || validTimeframe !== newUrlParams.timeframe) {
                    updateUrlPath(validSymbol, validTimeframe);
                }
            }
        };

        window.addEventListener('popstate', handlePopState);
        return () => window.removeEventListener('popstate', handlePopState);
    }, [symbol, timeframe, symbolList, timeframeList])

    // Debounced symbol change handler
    const debouncedSymbolChange = useCallback(
        debounce((newSymbol: string) => {
            setIsChangingSymbol(true);
            setSymbol(newSymbol);
            localStorage.setItem('symbol', newSymbol);

            // Update URL path with new symbol
            updateUrlPath(newSymbol, timeframe);

            // Reset changing state after a short delay to allow rendering to complete
            setTimeout(() => {
                setIsChangingSymbol(false);
            }, 300);
        }, 150), // 150ms debounce time
        [timeframe] // Include timeframe in dependencies
    );

    // Debounced timeframe change handler
    const debouncedTimeframeChange = useCallback(
        debounce((newTimeframe: string) => {
            setIsChangingTimeframe(true);
            setTimeframe(newTimeframe);
            localStorage.setItem('timeframe', newTimeframe);

            // Update URL path with new timeframe
            updateUrlPath(symbol, newTimeframe);

            // Reset changing state after a short delay to allow rendering to complete
            setTimeout(() => {
                setIsChangingTimeframe(false);
            }, 300);
        }, 150), // 150ms debounce time
        [symbol] // Include symbol in dependencies
    );

    const handleSymbolChange = (newSymbol: string) => {
        const now = Date.now();
        // Prevent changes that are too rapid (less than 100ms apart)
        if (now - lastSymbolChangeTime.current < 100) {
            return;
        }

        lastSymbolChangeTime.current = now;
        debouncedSymbolChange(newSymbol);
    };

    const handleTimeframeChange = (newTimeframe: string) => {
        const now = Date.now();
        // Prevent changes that are too rapid (less than 100ms apart)
        if (now - lastTimeframeChangeTime.current < 100) {
            return;
        }

        lastTimeframeChangeTime.current = now;
        debouncedTimeframeChange(newTimeframe);
    };


    return (
        <div ref={dashboardRef} style={{ height: '100vh', width: '100%', position: 'relative' }}>
            <Header>
                <LogoContainer>
                    <ConcentricRect level={1} />
                    <ConcentricRect level={2} />
                    <ConcentricRect level={3} />
                    <ConcentricRect level={4} />
                    <LogoText>
                        {/* Create a separate LogoChar for each letter with different animation delays */}
                        <LogoChar position={0}>S</LogoChar>
                        <LogoChar position={1}>o</LogoChar>
                        <LogoChar position={2}>l</LogoChar>
                        <LogoChar position={3}>a</LogoChar>
                        <LogoChar position={4}>r</LogoChar>
                        <LogoChar position={5}>i</LogoChar>
                        <LogoChar position={6}>s</LogoChar>
                    </LogoText>
                </LogoContainer>
                {/* Symbol selector - uses default arrow keys */}
                <Selector
                    value={symbol}
                    options={symbolList.map(symbol => ({ label: symbol.replace('usdt', '').toUpperCase(), value: symbol }))}
                    onSelectChange={handleSymbolChange}
                    enableKeyboardNavigation={true}
                    disabled={isChangingSymbol}
                />
                {/* Timeframe selector - uses < and > keys */}
                <Selector
                    value={timeframe}
                    options={timeframeList.map(timeframe => ({ label: timeframe, value: timeframe }))}
                    onSelectChange={handleTimeframeChange}
                    enableKeyboardNavigation={true}
                    disabled={isChangingSymbol || isChangingTimeframe}
                    customKeys={{
                        up: ['ArrowLeft'], // Use < or , to go to previous timeframe
                        down: ['ArrowRight']  // Use > or . to go to next timeframe
                    }}
                />
            </Header>
            <App
                enableToolbox
                enableTradingPanel
                symbol={symbol}
                timeframe={timeframe}
                loader={loader}
                isChangingSymbol={isChangingSymbol || isChangingTimeframe}
            />
            <SnapshotButton onClick={capturePage}>
                <SnapshotIcon color="rgb(7, 81, 207)" size={24} />
            </SnapshotButton>
        </div>
    )
}