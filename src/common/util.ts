import Konva from "konva";
import { T<PERSON>oord, <PERSON><PERSON><PERSON><PERSON> } from "../types";

export function timeframe2timeUnit(timeframe: string): number {
    switch (timeframe) {
        case '1m':
            return 60 * 1000
        case '15m':
            return 15 * 60 * 1000
        case '3m':
            return 3 * 60 * 1000
        case '5m':
            return 5 * 60 * 1000
        case '30m':
            return 30 * 60 * 1000
        case '1h':
            return 60 * 60 * 1000
        case '4h':
            return 4 * 60 * 60 * 1000
        case '1d':
            return 24 * 60 * 60 * 1000
        default:
            throw new Error('invalid timeframe')
    }
}


export function getFormatNumStr(num: number) {

    let text = "";
    if (Math.abs(num) < 0.4) {
        text = num.toFixed(5);
    } else if (Math.abs(num) < 1) {
        text = num.toFixed(4);
    }
    else {
        text = num.toFixed(3);
    }
    return text
}

export function findClosestDivisible(
    number: number,
    divisor: number,
    snapDirection: 'lower' | 'higher' | 'closest' = 'closest'
): number {
    const lower = Math.floor(number / divisor) * divisor;
    const higher = Math.ceil(number / divisor) * divisor;

    switch (snapDirection) {
        case 'lower':
            return lower;
        case 'higher':
            return higher;
        case 'closest':
        default:
            return Math.abs(number - lower) < Math.abs(higher - number) ? lower : higher;
    }
}


export const absCoord2GroupCoord = (group: Konva.Group, coord: TCoord) => {
    const groupPos = group.absolutePosition()
    return {
        x: coord.x - groupPos.x,
        y: coord.y - groupPos.y
    }
}

export const groupCoord2AbsCoord = (group: Konva.Group, coord: TCoord) => {
    const groupPos = group.absolutePosition()
    return {
        x: coord.x + groupPos.x,
        y: coord.y + groupPos.y
    }
}

export function getPct(num: number, digits: number = 2) {
    return (num * 100).toFixed(digits) + '%'
}

/**
 * Creates a debounced function that delays invoking func until after wait milliseconds have elapsed
 * since the last time the debounced function was invoked.
 *
 * @param func The function to debounce
 * @param wait The number of milliseconds to delay
 * @returns A debounced version of the original function
 */
export function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
    let timeout: ReturnType<typeof setTimeout> | null = null;

    return function (...args: Parameters<T>) {
        if (timeout) clearTimeout(timeout);
        timeout = setTimeout(() => {
            func(...args);
            timeout = null;
        }, wait);
    };
}

export function normalize(value: number, range1: TRange, range2: TRange) {
    if (range1.max === range1.min) return range2.min; // Avoid division by zero
    return range2.min + (value - range1.min) * (range2.max - range2.min) / (range1.max - range1.min);
}


export function isApp() {
    return window.matchMedia('(display-mode: standalone)').matches
}

