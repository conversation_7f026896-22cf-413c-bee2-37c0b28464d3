import React, { JSX, useState, useContext } from 'react';
import { AppContext } from '../contexts/app.context';
import {
  AlertListContainer,
  AlertListItem,
  AlertListEmpty
} from '../styles/alertList.styles';
import { TAlertConfig } from '../types';
import { useNotification } from '../hooks/useNotification';
import { getFormatNumStr } from '../common/util';
import { useAlert } from '../hooks/useAlert';

export const AlertList: React.FC<{ alertConfigList: TAlertConfig[] }> = (props) => {
  const [crossedAlerts, setCrossedAlerts] = useState<string[]>([]);
  const { alertConfigList } = props;
  const { addNotification } = useNotification();
  const { deleteAlert } = useAlert();
  const { symbol, timeframe } = useContext(AppContext);

  // Handle double-click on alert item
  const handleDoubleClick = (alert: TAlertConfig) => {
    // Add to crossed list first for visual feedback
    setCrossedAlerts((prev) => [...prev, alert.id]);

    // After a short delay for the visual effect, delete the alert
    setTimeout(() => {
      // Show notification
      addNotification({
        message: 'Alert deleted successfully',
        type: 'success',
        duration: 2000
      });

      // Delete the alert and keep it crossed out until deletion is complete
      deleteAlert(alert.id)
        .then(() => {
          // The alert will be removed from the list by the parent component
          // when alertConfigList is updated, so we don't need to remove it from crossedAlerts
        })
        .catch((error) => {
          // If deletion fails, remove from crossed list
          setCrossedAlerts((prev) => prev.filter((id) => id !== alert.id));

          addNotification({
            message: `Failed to delete alert: ${error.message || 'Unknown error'}`,
            type: 'error',
            duration: 3000
          });
        });
    }, 500); // Slightly longer delay for better visual feedback
  };

  // Format alert description
  const formatAlertDescription = (alert: TAlertConfig): JSX.Element | string => {
    let description: JSX.Element | string;

    // For price alerts
    if (alert.arg2?.type === 'value') {
      const isCurrentSymbolAndTimeframe = alert.symbol === symbol && alert.timeframe === timeframe;
      description = <span style={{ textDecoration: isCurrentSymbolAndTimeframe ? 'underline' : 'none' }}>
        Alert at <strong>{getFormatNumStr(alert.arg2.value!)}</strong> &nbsp; ({alert.symbol?.replace('usdt', '').toUpperCase()}/{alert.timeframe})
      </span>
    }
    // For drawing alerts
    else if (alert.arg2?.type === 'drawing') {
      const isCurrentSymbolAndTimeframe = alert.symbol === symbol && alert.timeframe === timeframe;
      description = <span style={{ textDecoration: isCurrentSymbolAndTimeframe ? 'underline' : 'none' }}>
        Alert for <strong>{`${alert.arg2.name} ${alert.arg2.refId.slice(-4)}`}</strong> &nbsp; ({alert.symbol?.replace('usdt', '').toUpperCase()}/{alert.timeframe})
      </span>
    }
    // Generic fallback
    else {
      description = `Alert ${alert.id.substring(0, 6)}`;
    }

    return description;
  };

  return (
    <AlertListContainer>
      {alertConfigList.length === 0 ? (
        <AlertListEmpty><span style={{ direction: 'ltr', display: 'inline-block' }}>No alerts yet</span></AlertListEmpty>
      ) : (
        alertConfigList.map((alert) => (
          <AlertListItem
            key={alert.id}
            crossed={crossedAlerts.includes(alert.id)}
            onDoubleClick={() => handleDoubleClick(alert)}
          >
            <span style={{ direction: 'ltr', display: 'inline-block' }}>
              {formatAlertDescription(alert)}
            </span>
          </AlertListItem>
        ))
      )}
    </AlertListContainer>
  );
};
