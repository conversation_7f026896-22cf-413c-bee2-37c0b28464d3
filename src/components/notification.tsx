import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import {
  NotificationContainer,
  NotificationItem,
  NotificationIcon,
  NotificationContent,
  NotificationClose
} from '../styles/notification.styles';
import { TNotificationProps } from '../types';
import { NotificationContext, useNotificationProvider } from '../hooks/useNotification';

// Notification component to display notifications
export const Notification: React.FC<TNotificationProps> = ({ notifications, onRemove }) => {
  const [exitingIds, setExitingIds] = useState<string[]>([]);

  const handleRemove = (id: string) => {
    setExitingIds((prev) => [...prev, id]);
    setTimeout(() => {
      onRemove(id);
      setExitingIds((prev) => prev.filter((exitId) => exitId !== id));
    }, 300); // Match animation duration
  };

  useEffect(() => {
    notifications.forEach((notification) => {
      if (notification.duration) {
        const timer = setTimeout(() => {
          handleRemove(notification.id);
        }, notification.duration);

        return () => clearTimeout(timer);
      }
    });
  }, [notifications]);

  return createPortal(
    <NotificationContainer>
      {notifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          type={notification.type}
          className={exitingIds.includes(notification.id) ? 'exiting' : ''}
        >
          <NotificationIcon>
            {notification.type === 'success' ? '✅' : '❌'}
          </NotificationIcon>
          <NotificationContent>{notification.message}</NotificationContent>
          <NotificationClose onClick={() => handleRemove(notification.id)}>✕</NotificationClose>
        </NotificationItem>
      ))}
    </NotificationContainer>,
    document.body
  );
};

// Notification provider component
export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const {
    notifications,
    addNotification,
    removeNotification,
    requestNotificationPermission,
    hasNotificationPermission,
    subscribeToPushNotifications,
    unsubscribeFromPushNotifications,
    isPushSubscribed
  } = useNotificationProvider();

  return (
    <NotificationContext.Provider value={{
      addNotification,
      removeNotification,
      requestNotificationPermission,
      hasNotificationPermission,
      subscribeToPushNotifications,
      unsubscribeFromPushNotifications,
      isPushSubscribed
    }}>
      {children}
      <Notification notifications={notifications} onRemove={removeNotification} />
    </NotificationContext.Provider>
  );
};
