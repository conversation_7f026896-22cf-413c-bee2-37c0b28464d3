import React, { useContext, useEffect, useState } from 'react';
import { ContextMenuContainer, ContextMenuItem, ContextMenuDivider } from '../styles/contextMenu.styles';
import { getFormatNumStr } from '../common/util';
import { fetchAlertTriggerTypeList } from '../common/api';
import { PaneContext } from '../contexts/pane.context';
import { TContextMenuProps } from '../types';
import { useNotification } from '../hooks/useNotification';
import { useAlert } from '../hooks/useAlert';

export const ContextMenu: React.FC<TContextMenuProps> = ({
  state,
  menuRef,
  onClose,
}) => {

  const [triggerTypes, setTriggerTypes] = useState<Array<{ type: string; name: string }>>([]);
  const { selectedDrawingConfig } = useContext(PaneContext);
  const [selectedAlertType, setSelectedAlertType] = useState<string | null>(null);
  const { addNotification } = useNotification();
  const { addDrawingAlert, addPriceAlert } = useAlert()

  useEffect(() => {
    fetchAlertTriggerTypeList().then(setTriggerTypes);
  }, []);

  const onFinish = (alertDescription: string) => {
    addNotification({
      message: `${alertDescription} created successfully`,
      type: 'success',
      duration: 3000
    });
  }
  const onFail = (alertDescription: string, error: any) => {
    addNotification({
      message: `Failed to create ${alertDescription}: ${error.message || 'Unknown error'}`,
      type: 'error',
      duration: 5000
    });
  }

  // Handle adding an alert
  const addAlert = (triggerType: string) => {
    let alertDescription: string = '';

    if (selectedAlertType === 'drawing') {
      alertDescription = `Alert for ${selectedDrawingConfig!.type}`;
      addDrawingAlert(selectedDrawingConfig!, triggerType)
        .then(() => onFinish(alertDescription))
        .catch(error => onFail(alertDescription, error))
    } else if (selectedAlertType === 'price') {
      alertDescription = `Alert at ${getFormatNumStr(state.price)}`;
      addPriceAlert(state.price, triggerType)
        .then(() => onFinish(alertDescription))
        .catch(error => onFail(alertDescription, error))
    }

    setSelectedAlertType(null);
    onClose();
  };

  return (
    <ContextMenuContainer
      ref={menuRef}
      style={{
        position: 'fixed',
        top: state.y,
        left: state.x
      }}
      onClick={(e: React.MouseEvent) => e.stopPropagation()}
      onContextMenu={(e: React.MouseEvent) => e.preventDefault()}
    >
      <ContextMenuItem onClick={() => setSelectedAlertType('price')}>
        <span>Add an alert 🔔 at {getFormatNumStr(state.price)}</span>
      </ContextMenuItem>
      {
        selectedDrawingConfig && (
          <ContextMenuItem onClick={() => setSelectedAlertType('drawing')}>
            <span>Add an alert 🔔 for {selectedDrawingConfig.type}</span>
          </ContextMenuItem>
        )
      }

      {selectedAlertType && (
        <>
          <ContextMenuDivider />
          <div style={{ padding: '5px 14px', fontSize: '12px', color: 'rgba(7, 81, 207, 0.7)' }}>
            Select trigger type:
          </div>
          {triggerTypes.map(triggerType => (
            <ContextMenuItem
              key={triggerType.type}
              onClick={() => addAlert(triggerType.type)}
            >
              <span>{triggerType.name}</span>
            </ContextMenuItem>
          ))}
        </>
      )}

      <ContextMenuDivider />
      <ContextMenuItem onClick={(e) => {
        e.stopPropagation();
        onClose();
      }}>
        <span>❌ Cancel</span>
      </ContextMenuItem>
    </ContextMenuContainer>
  );
};
