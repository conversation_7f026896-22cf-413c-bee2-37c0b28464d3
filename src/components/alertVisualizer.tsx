import { useContext, useMemo } from 'react';
import { Group, Line } from 'react-konva';
import { AppContext } from '../contexts/app.context';
import { useScale } from '../hooks/useScale';
import { ChartContext } from '../contexts/chart.context';
import { ALERT_COLOR } from '../common/constants';


export const AlertVisualizer = () => {
  const { alertConfigList, width } = useContext(AppContext);
  const { chartData } = useContext(ChartContext);
  const { v2y } = useScale();

  // Filter price alerts for the current chart
  const priceAlerts = useMemo(() => {
    if (!chartData) return [];
    return alertConfigList.filter(alert =>
      alert.chart === chartData.name &&
      alert.arg2?.type === 'value'
    );
  }, [alertConfigList, chartData]);

  // Render price alerts as horizontal dotted lines
  const renderPriceAlerts = () => {
    return priceAlerts.map(alert => {
      const price = alert.arg2?.value;
      if (price === undefined) return null;

      const y = v2y(price);
      return (
        <Line
          key={`price-alert-${alert.id}`}
          points={[0, y, width, y]}
          stroke={ALERT_COLOR}
          strokeWidth={1.5}
          dash={[5, 5]} // Dotted line
        />
      );
    });
  };

  if (!chartData || priceAlerts.length === 0) {
    return null;
  }

  return (
    <Group>
      {renderPriceAlerts()}
    </Group>
  );
};
