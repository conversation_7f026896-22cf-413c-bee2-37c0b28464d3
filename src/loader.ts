import * as api from "./common/api";
import { TLoader } from "./types";

const apiHost = 'http://localhost:5090'
const wsHost = 'ws://localhost:4090'

export class Loader implements TLoader {
    drawingConfig = {
        getList: api.fetchDrawingConfigs,
        create: api.createDrawingConfig,
        delete: api.deleteDrawingConfig,
        update: api.updateDrawingConfig,
        clear: api.clearDrawingConfigs
    }


    subscribeData(symbol: string, timeframe: string, callback: (data: { topic: string; data: any; }) => void): WebSocket {
        const ws = new WebSocket(`${wsHost}?topic=chartdata:${symbol}:${timeframe},tick:${symbol}:${timeframe},position`)
        ws.onmessage = (event) => {
            const data = JSON.parse(event.data)
            callback(data)
        }
        return ws
    }
    async getChartDataList(params: {
        symbol: string,
        timeframe: string,
        startTime?: number,
        endTime?: number,
        limit?: number
    }) {
        const chartData = await fetch(`${apiHost}/chart-data?symbol=${params.symbol}&timeframe=${params.timeframe}${params.limit ? `&limit=${params.limit}` : ''}${params.startTime ? `&startTime=${params.startTime}` : ''}${params.endTime ? `&endTime=${params.endTime}` : ''}`)
            .then(res => res.json())
        return chartData
    }

}