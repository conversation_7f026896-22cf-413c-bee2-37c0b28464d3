import { TLoader } from './loader.types';
import { TRange } from './common.types';
import { TPlotData } from './plot.types';

export interface TSolarisProps {
    symbol: string;
    timeframe: string;
    loader: TLoader;
    defaultTimeRange?: TRange;
    enableToolbox?: boolean;
    enableTradingPanel?: boolean;
    isChangingSymbol?: boolean;
}

export interface TSolarisHandle {
    updateData: (id: string, data: any[]) => void;
    updateTick: (tick: TTick) => void;
    loadData: (data: TChartData[]) => void;
    appendData: (symbol: string, timeframe: string, data: TChartData[]) => void;
    prependData: (symbol: string, timeframe: string, data: TChartData[]) => void;
}

export interface TChartData {
    name: string;
    plots: TPlotData[];
}

export interface TTick {
    time: number;
    price: number;
}

export type TStreamPlotData = {
    type: string;
    id: string;
    name: string;
    data: any;
}