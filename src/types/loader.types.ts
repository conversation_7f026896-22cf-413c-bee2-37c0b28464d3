import { TChartData, TStreamPlotData, TTick } from './chart.types';
import { TDataPoint } from './plot.types';
import { TDrawingConfig } from './tools.types';
import { TPosition } from './trading.types';

export abstract class TLoader {
    abstract drawingConfig: {
        getList: (params: { symbol: string, chart?: string }) => Promise<TDrawingConfig[]>,
        create: (params: { symbol: string, chart: string, type: string, dataPoints: TDataPoint[] }) => Promise<TDrawingConfig>,
        delete: (id: string) => Promise<void>,
        update: (id: string, dataPoints: TDataPoint[]) => Promise<void>,
        clear: (symbol: string) => Promise<void>
    }

    abstract getChartDataList(params: {
        symbol: string,
        timeframe: string,
        startTime?: number,
        endTime?: number,
        limit?: number
    }): Promise<TChartData[]>;

    abstract subscribeData(
        symbol: string,
        timeframe: string,
        callback: (data: {
            topic: string,
            updates?: TStreamPlotData[],
            tick?: TTick,
            positions?: TPosition[]
        }) => void): WebSocket;
}