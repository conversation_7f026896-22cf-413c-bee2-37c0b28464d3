import { TDataPoint } from './plot.types';

// Define the tool types
export enum ETool {
    PriceRange = 'priceRange',
    Ray = 'ray',
    Note = 'note',
    None = '',
    TimeRange = 'timeRange',
    RangeTradeDist = 'rangeTradeDist',
    HorizontalRayChannel = 'horizontalRayChannel'
}

export type TDrawingConfig = {
    id?: string;
    symbol: string;
    timeframe: string;
    chart: string;
    type: ETool;
    dataPoints: TDataPoint[];
}

export const EToolControlDotCount = {
    [ETool.PriceRange]: 2,
    [ETool.Ray]: 2,
    [ETool.Note]: 1,
    [ETool.None]: 0,
    [ETool.TimeRange]: 2,
    [ETool.RangeTradeDist]: 2
}


