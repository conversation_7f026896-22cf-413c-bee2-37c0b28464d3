export interface TRange {
    max: number;
    min: number;
}

export interface TSize {
    width: number;
    height: number;
}

export interface TCoord {
    x: number;
    y: number;
}

export interface TCrosshairsData {
    time: number;
    value: number;
}

export interface TContextMenuState {
    x: number;
    y: number;
    price: number;
}

export interface TContextMenuProps {
    state: TContextMenuState;
    menuRef: React.RefObject<HTMLDivElement | null>;
    onClose: () => void;
}