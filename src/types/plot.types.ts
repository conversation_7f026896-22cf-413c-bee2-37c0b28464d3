import { TRange } from "./common.types";

export enum EPlot {
    PriceLine = 'priceline',
    Line = 'line',
    Scatter = 'scatter',
    Histogram = 'histogram',
    Candlestick = 'candlestick',
    ThreeBars = 'threeBars',
}

export interface TBasePlotData {
    type: EPlot;
    name: string;
    id: string;
    config?: Record<string, any>;
}

export type TPlotData = TBasePlotData &
    (
        | { type: EPlot.Line; data: TDataPoint[] }
        | { type: EPlot.Histogram; data: TDataPoint[] }
        | { type: EPlot.Candlestick; data: TCandlestick[] }
        | { type: EPlot.PriceLine; data: number }
        | { type: EPlot.Scatter; data: TScatter[] }
    )

export interface TDataPoint {
    time: number;
    value: number | null | undefined;
    color?: string;
}

export interface TCandlestick {
    open: number;
    close: number;
    high: number;
    low: number;
    time: number;
    color?: string;
}

export interface TScatter {
    time: number;
    value: number;
    color?: string;
    shape: string;
    size: number;
    position: string;
}

export interface TTradeDist {
    timeRange: TRange;
    data: number[];
}