import { useContext, useMemo, useRef } from "react";
import { Circle, Group, Line, Shape } from "react-konva";
import Konva from "konva";
import { useDrawing } from "../hooks/useDrawing";
import { TDrawingConfig } from "../types";
import { AppContext } from "../contexts/app.context";

export function HorizontalRayChannel(props: { config: TDrawingConfig, onDrawEnd?: () => void | Promise<void> }) {
    const groupRef = useRef<Konva.Group>(null);
    const { width } = useContext(AppContext)

    const {
        previewCoord,
        anchorCoords,
        drawingColor,
        lineDash,
        handleDragEnd,
        handleDragMove,
        handleDragStart,
        handleMouseOver,
        handleMouseOut,
        groupDragBoundFunc,
        anchorDragBoundFunc,
        handleClick,
        toRelativeX
    } = useDrawing({ groupRef, config: props.config, anchorCount: 2, onDrawEnd: props.onDrawEnd });

    const linePointsList = useMemo(() => {
        if (anchorCoords.length === 0) return []
        const leftX = Math.min(anchorCoords[0].x, anchorCoords[1]?.x ?? previewCoord?.x)
        const firstLinePoints = [leftX, anchorCoords[0].y, toRelativeX(width), anchorCoords[0].y]
        const secondLinePoints = [leftX, anchorCoords[1]?.y ?? previewCoord?.y, toRelativeX(width), anchorCoords[1]?.y ?? previewCoord?.y]
        return [firstLinePoints, secondLinePoints]
    }, [anchorCoords, previewCoord])

    const fillColor = useMemo(() => {
        return `${drawingColor}10`; // Adds semi-transparency to the fill
    }, [drawingColor]);

    const y1 = anchorCoords[0]?.y ?? previewCoord?.y;
    const y2 = anchorCoords[1]?.y ?? previewCoord?.y;
    const leftX = Math.min(anchorCoords[0]?.x ?? previewCoord?.x, anchorCoords[1]?.x ?? previewCoord?.x);
    const rightX = toRelativeX(width);

    return (
        <Group
            ref={groupRef}
            draggable
            onDragStart={handleDragStart}
            dragBoundFunc={groupDragBoundFunc}
            onDragMove={handleDragMove}
            onDragEnd={handleDragEnd}
            onClick={handleClick}
            onMouseOver={handleMouseOver}
            onMouseOut={handleMouseOut}
        >
            {/* Fill shape between the two lines */}
            {y1 !== undefined && y2 !== undefined && (
                <Shape
                    sceneFunc={(context, shape) => {
                        context.beginPath();
                        context.moveTo(leftX, y1);
                        context.lineTo(rightX, y1);
                        context.lineTo(rightX, y2);
                        context.lineTo(leftX, y2);
                        context.closePath();
                        context.fillStrokeShape(shape);
                    }}
                    fill={fillColor}
                />
            )}

            {/* First horizontal line */}
            <Line
                points={linePointsList[0]}
                stroke={drawingColor}
                strokeWidth={1.4}
                hitStrokeWidth={10}
                dash={lineDash}
            />

            {/* Second horizontal line */}
            <Line
                points={linePointsList[1]}
                stroke={drawingColor}
                strokeWidth={1.4}
                hitStrokeWidth={10}
                dash={lineDash}
            />

            {anchorCoords.map((coord, index) => (
                <Circle
                    name="anchor"
                    key={`anchor-${index}`}
                    x={coord.x}
                    y={coord.y}
                    radius={5}
                    fill="white"
                    stroke={drawingColor}
                    strokeWidth={2}
                    draggable
                    dragBoundFunc={anchorDragBoundFunc}
                />
            ))}
        </Group>
    );
}