// import { useContext, useMemo, useRef } from "react";
// import { Circle, Group, Line, Text, Arrow } from "react-konva";
// import Konva from "konva";
// import { AppContext } from "../contexts/app.context";
// import { useDrawing } from "../hooks/useDrawing";
// import { useScale } from "../hooks/useScale";
// import { TDrawingConfig } from "../types";

// // Readable time string function
// function formatTimeDiff(timeDiffMs: number): string {
//     const absTimeDiffMs = Math.abs(timeDiffMs);
//     const days = Math.floor(absTimeDiffMs / (1000 * 60 * 60 * 24));
//     const hours = Math.floor((absTimeDiffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
//     const minutes = Math.floor((absTimeDiffMs % (1000 * 60 * 60)) / (1000 * 60));

//     const parts: string[] = [];
//     if (days > 0) parts.push(`${days}d`);
//     if (hours > 0) parts.push(`${hours}h`);
//     if (minutes > 0) parts.push(`${minutes}m`);

//     if (parts.length === 0) return "0m";

//     return (timeDiffMs < 0 ? "-" : "") + parts.join(" ");
// }

// export function TimeRange(props: { config: TDrawingConfig }) {
//     const groupRef = useRef<Konva.Group>(null);
//     const { crosshairsCoord } = useContext(AppContext);
//     const {
//         dataPoints,
//         getGroupCoord,
//         onDragEnd,
//         onDragMove,
//         onMouseOver,
//         onMouseOut,
//         onClick,
//         drawingColor,
//         lineDash,
//         groupDragBoundFunc,
//         onDragStart,
//         anchorDragBoundFunc
//     } = useDrawing({ groupRef, config: props.config, anchorCount: 2 });
//     const { t2x, v2y, x2t } = useScale();

//     const timeRangeData = useMemo(() => {
//         if (dataPoints.length === 0) return null;

//         // First point
//         const firstPoint = {
//             x: t2x(dataPoints[0].time),
//             y: v2y(dataPoints[0].value!)
//         };

//         // Second point (or use crosshairs if only one point)
//         const secondPoint = dataPoints.length === 1
//             ? { x: crosshairsCoord?.x || 0, y: crosshairsCoord?.y || 0 }
//             : { x: t2x(dataPoints[1].time), y: v2y(dataPoints[1].value as number) };

//         // Determine which point is left and which is right
//         const [leftPoint, rightPoint] = firstPoint.x < secondPoint.x
//             ? [firstPoint, secondPoint]
//             : [secondPoint, firstPoint];

//         // Calculate time difference (always second - first, regardless of position)
//         const firstTime = dataPoints[0].time;
//         const secondTime = dataPoints.length === 1
//             ? (crosshairsCoord ? x2t(crosshairsCoord.x) : 0)
//             : dataPoints[1].time;

//         const timeDiffMs = secondTime - firstTime;
//         const timeDiffFormatted = formatTimeDiff(timeDiffMs); // Use formatted string

//         // Convert to group coordinates
//         const firstGroupCoord = getGroupCoord(firstPoint);
//         const secondGroupCoord = getGroupCoord(secondPoint);
//         const leftGroupCoord = getGroupCoord(leftPoint);
//         const rightGroupCoord = getGroupCoord(rightPoint);

//         // Create vertical lines at each point
//         const firstLinePoints = [firstGroupCoord.x, leftGroupCoord.y, firstGroupCoord.x, rightGroupCoord.y];
//         const secondLinePoints = [secondGroupCoord.x, leftGroupCoord.y, secondGroupCoord.x, rightGroupCoord.y];

//         // Create horizontal arrow at the vertical center, pointing from first to second
//         const centerY = (leftGroupCoord.y + rightGroupCoord.y) / 2;
//         const horizontalPoints = [
//             firstGroupCoord.x,
//             centerY,
//             secondGroupCoord.x,
//             centerY
//         ];

//         // Determine if time change is positive (for arrow direction)
//         const isPositive = timeDiffMs >= 0;

//         return {
//             firstLinePoints,
//             secondLinePoints,
//             horizontalPoints,
//             firstGroupCoord,
//             secondGroupCoord,
//             timeDiffFormatted, // Replace timeDiffHours
//             isPositive
//         };
//     }, [dataPoints, t2x, v2y, crosshairsCoord, getGroupCoord, x2t]);

//     if (!timeRangeData) return null;

//     return (
//         <Group
//             ref={groupRef}
//             draggable
//             onDragMove={onDragMove}
//             onDragEnd={onDragEnd}
//             onMouseOver={onMouseOver}
//             onMouseOut={onMouseOut}
//             onClick={onClick}
//             dragBoundFunc={groupDragBoundFunc}
//             onDragStart={onDragStart}
//         >
//             {/* First vertical line */}
//             <Line
//                 points={timeRangeData.firstLinePoints}
//                 stroke={drawingColor}
//                 strokeWidth={1.4}
//                 hitStrokeWidth={10}
//                 dash={lineDash}
//             />

//             {/* Second vertical line */}
//             <Line
//                 points={timeRangeData.secondLinePoints}
//                 stroke={drawingColor}
//                 strokeWidth={1.4}
//                 hitStrokeWidth={10}
//                 dash={lineDash}
//             />

//             {/* Horizontal arrow pointing to second line */}
//             <Arrow
//                 points={timeRangeData.horizontalPoints}
//                 stroke={drawingColor}
//                 strokeWidth={1.4}
//                 hitStrokeWidth={10}
//                 dash={lineDash}
//                 pointerLength={6}
//                 pointerWidth={6}
//                 fill={drawingColor}
//                 opacity={0.6}
//             />

//             {/* Time difference label - positioned above the horizontal arrow */}
//             <Text
//                 x={(timeRangeData.firstGroupCoord.x + timeRangeData.secondGroupCoord.x) / 2 - 50}
//                 y={timeRangeData.horizontalPoints[1] - 20}
//                 text={timeRangeData.timeDiffFormatted} // Use formatted string
//                 fontSize={12}
//                 fill={drawingColor}
//                 width={100}
//                 align="center"
//             />

//             {/* Control dots */}
//             {dataPoints.map((dp, index) => {
//                 const coord = {
//                     x: t2x(dp.time),
//                     y: v2y(dp.value!)
//                 };
//                 const groupCoord = getGroupCoord(coord);
//                 return (
//                     <Circle
//                         name="anchor"
//                         id={`${index}`}
//                         draggable
//                         key={`anchor-${index}`}
//                         x={groupCoord.x}
//                         y={groupCoord.y}
//                         radius={5}
//                         fill="white"
//                         stroke={drawingColor}
//                         strokeWidth={2}
//                         dragBoundFunc={anchorDragBoundFunc}
//                     />
//                 );
//             })}
//         </Group>
//     );
// }