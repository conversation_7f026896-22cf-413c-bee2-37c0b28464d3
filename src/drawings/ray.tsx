import { useMemo, useRef } from "react";
import { Circle, Group, Line } from "react-konva";
import Konva from "konva";
import { useDrawing } from "../hooks/useDrawing";
import { TDrawingConfig } from "../types";
import { useContext } from "react";
import { AppContext } from "../contexts/app.context";

export function Ray(props: { config: TDrawingConfig, onDrawEnd?: () => void | Promise<void> }) {
    const { width } = useContext(AppContext)
    const groupRef = useRef<Konva.Group>(null);
    const {
        previewCoord,
        anchorCoords,
        drawingColor,
        lineDash,
        handleDragEnd,
        handleDragMove,
        handleDragStart,
        handleMouseOver,
        handleMouseOut,
        groupDragBoundFunc,
        anchorDragBoundFunc,
        toRelativeX,
        handleClick,
    } = useDrawing({ groupRef, config: props.config, anchorCount: 2, onDrawEnd: props.onDrawEnd });

    const points = useMemo(() => {
        if (anchorCoords.length === 0) return []
        const firstCoord = anchorCoords[0]
        let secondCoord = anchorCoords[1] ?? previewCoord
        const slope = (secondCoord.y - firstCoord.y) / (secondCoord.x - firstCoord.x);
        const intercept = firstCoord.y - slope * firstCoord.x;

        // Determine direction based on points' positions
        const direction = secondCoord.x < firstCoord.x ? -1 : 1;
        const rayEndX = toRelativeX(direction > 0 ? width : 0)
        const rayEndY = slope * rayEndX + intercept;

        return [firstCoord.x, firstCoord.y, rayEndX, rayEndY]
    }, [anchorCoords, previewCoord])


    return (
        <Group
            ref={groupRef}
            draggable
            onDragStart={handleDragStart}
            dragBoundFunc={groupDragBoundFunc}
            onDragMove={handleDragMove}
            onDragEnd={handleDragEnd}
            onClick={handleClick}
            onMouseOver={handleMouseOver}
            onMouseOut={handleMouseOut}
        >
            <Line
                points={points}
                stroke={drawingColor}
                strokeWidth={1.4}
                hitStrokeWidth={10}
                dash={lineDash}
            />
            {
                anchorCoords.map((coord, index) => {
                    return (
                        <Circle
                            name="anchor"
                            key={`anchor-${index}`}
                            x={coord.x}
                            y={coord.y}
                            radius={5}
                            fill="white"
                            stroke={drawingColor}
                            strokeWidth={2}
                            draggable
                            dragBoundFunc={anchorDragBoundFunc}
                        />
                    );
                })}
        </Group>
    );
}