import { useEffect, useMemo, useRef, useState } from "react";
import { Circle, Group, Line, Rect } from "react-konva";
import Konva from "konva";
import { useDrawing } from "../hooks/useDrawing";
import { TDrawingConfig } from "../types";
import { useContext } from "react";
import { ChartContext } from "../contexts/chart.context";
import { fetchTradeDist } from "../common/api";
import { AppContext } from "../contexts/app.context";
import { RangeTradeDistShape } from "./RangeTradeDistShape";

const barCount = 7

export function RangeTradeDist(props: { config: TDrawingConfig, onDrawEnd?: () => void | Promise<void> }) {
    const { height } = useContext(ChartContext)
    const { symbol } = useContext(AppContext)
    const groupRef = useRef<Konva.Group>(null);
    const [isLoading, setIsLoading] = useState(false)
    const [distData, setDistData] = useState<number[]>([])

    const loadData = async () => {
        setIsLoading(true)
        setDistData([])
        try {
            const distData = await fetchTradeDist({
                symbol,
                timeframe: props.config.timeframe,
                startTime: dataPoints[0].time,
                endTime: dataPoints[1].time
            })
            setDistData(distData)
        } finally {
            setIsLoading(false)
        }
    }


    const {
        previewCoord,
        anchorCoords,
        drawingColor,
        lineDash,
        handleDragEnd,
        handleDragMove,
        handleDragStart,
        handleMouseOver,
        handleMouseOut,
        groupDragBoundFunc,
        anchorDragBoundFunc,
        handleClick,
        toRelativeY,
        isDrawing,
        isDraging,
        dataPoints
    } = useDrawing({
        groupRef, config: props.config, anchorCount: 2, onDrawEnd: () => {
            props.onDrawEnd?.()
            loadData()
        },
    });

    const animationRef = useRef<Konva.Animation | null>(null);



    const firstLinePoints = useMemo(() => {
        if (anchorCoords.length === 0) return []
        return [anchorCoords[0].x, toRelativeY(0), anchorCoords[0].x, toRelativeY(height)]
    }, [anchorCoords, previewCoord])

    const secondLinePoints = useMemo(() => {
        if (anchorCoords.length === 0) return []
        return [anchorCoords[1]?.x ?? previewCoord?.x, toRelativeY(0), anchorCoords[1]?.x ?? previewCoord?.x, toRelativeY(height)]
    }, [anchorCoords, previewCoord])

    const loadingBarAreaProps = useMemo(() => {
        if (anchorCoords.length !== 2) return null
        const barAreaheight = Math.min(Math.abs(anchorCoords[1].x - anchorCoords[0].x), 200)
        return {
            height: barAreaheight,
            width: barAreaheight * 0.8,
            x: Math.min(anchorCoords[0].x, anchorCoords[1].x),
            y: toRelativeY(height / 2 - barAreaheight / 2)
        }
    }, [anchorCoords])



    useEffect(() => {
        if (!groupRef.current) {
            if (animationRef.current) {
                animationRef.current.stop();
                animationRef.current = null;
            }
            return;
        }
        if (!isLoading) {
            if (animationRef.current) {
                animationRef.current.stop();
                animationRef.current = null;
            }
            return
        }

        const bars = groupRef.current.find(".loading-bar") as Konva.Rect[];

        if (anchorCoords.length === 2) {
            animationRef.current = new Konva.Animation((frame) => {
                const time = (frame?.time || 0) / 1000; // Time in seconds

                // Animate bars
                bars.forEach((bar, index) => {
                    const phase = (index / barCount) * Math.PI * 2; // Stagger each bar
                    const pulse = 0.5 + 0.5 * Math.sin(time * Math.PI * 2 + phase); // 0 to 1
                    bar.opacity(0.2 + 0.2 * pulse); // Opacity 0.2 to 0.4
                    bar.scaleX(0.5 + 0.5 * pulse); // Width 50% to 100% of barAreaWidth
                });
            }, groupRef.current.getLayer());

            animationRef.current.start();
        }

        return () => {
            if (animationRef.current) {
                animationRef.current.stop();
                animationRef.current = null;
            }
        };
    }, [anchorCoords.length, isLoading]);

    useEffect(() => {
        if (props.config.id) {
            loadData()
        }
    }, [])

    return (
        <Group
            ref={groupRef}
            onDragStart={handleDragStart}
            dragBoundFunc={groupDragBoundFunc}
            onDragMove={handleDragMove}
            onDragEnd={() => {
                handleDragEnd()
                loadData()
            }}
            onClick={handleClick}
            onMouseOver={handleMouseOver}
            onMouseOut={handleMouseOut}
        >
            {
                loadingBarAreaProps && isLoading && Array(barCount).fill('').map((_, index) => (
                    <Rect
                        key={`loading-bar-${index}`}
                        x={loadingBarAreaProps.x}
                        y={loadingBarAreaProps.y + index * loadingBarAreaProps.height / barCount}
                        width={loadingBarAreaProps.width}
                        height={loadingBarAreaProps.height / barCount - 2}
                        fill={drawingColor}
                        opacity={0.7}
                        name="loading-bar"
                    />
                ))
            }
            {
                !isLoading && !isDraging && distData && dataPoints.length === 2
                && <RangeTradeDistShape
                    timeRange={{
                        min: dataPoints[0].time,
                        max: dataPoints[1].time
                    }}
                    distData={distData}
                />
            }

            <Line
                visible={isDrawing || isDraging || isLoading}
                points={firstLinePoints}
                stroke={drawingColor}
                strokeWidth={1.4}
                hitStrokeWidth={10}
                dash={lineDash}
            />

            <Line
                visible={isDrawing || isDraging || isLoading}
                points={secondLinePoints}
                stroke={drawingColor}
                strokeWidth={1.4}
                hitStrokeWidth={10}
                dash={lineDash}
            />
            {
                anchorCoords.map((coord, index) => {
                    return (
                        <Circle
                            name="anchor"
                            key={`anchor-${index}`}
                            x={coord.x}
                            y={coord.y}
                            radius={5}
                            fill="white"
                            stroke={drawingColor}
                            strokeWidth={2}
                            draggable
                            dragBoundFunc={anchorDragBoundFunc}
                        />
                    );
                })}
        </Group>
    );
}