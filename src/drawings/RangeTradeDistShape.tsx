import { Shape } from "react-konva";
import { use<PERSON><PERSON>back, useContext, useMemo } from "react";
import Konva from "konva";
import { useScale } from "../hooks/useScale";
import { ChartContext } from "../contexts/chart.context";
import { AppContext } from "../contexts/app.context";
import { TRange } from "../types";


// Function to generate a random color with transparency
const generateRandomColor = () => {
    const r = Math.floor(Math.random() * 256);
    const g = Math.floor(Math.random() * 256);
    const b = Math.floor(Math.random() * 256);
    return `rgba(${r}, ${g}, ${b}, 0.5)`; // 0.5 alpha for semi-transparency
};

export function RangeTradeDistShape(props: {
    barCount?: number,
    barSpacing?: number,
    timeRange: TRange,
    distData: number[]
}) {
    const { t2x, v2y, deltaT2x } = useScale();
    const { chartData } = useContext(ChartContext);
    const { timeRange, timeUnit } = useContext(AppContext);

    // Generate a random color when the component initializes
    // Using useMemo to ensure the color stays consistent during re-renders
    const randomColor = useMemo(() => generateRandomColor(), []);


    const tradeDistProps = useMemo(() => {
        if (props.timeRange.min < timeRange.min || props.timeRange.max > timeRange.max) {
            // Skip rendering if the profile is outside the visible time range
            return null
        }

        if (!props.distData) return null
        if (!chartData) return null


        const plotData = chartData.mainPlot
        if (!plotData) return null
        const startTime = Math.min(props.timeRange.min, props.timeRange.max)
        const endTime = Math.max(props.timeRange.min, props.timeRange.max)
        const barCount = props.barCount || 7

        const valueRange = plotData.getValueRangeByTime(startTime, endTime)
        if (!valueRange) return null
        const timeUnitWidth = deltaT2x(timeUnit);
        const distBarAreaHeight = Math.abs(v2y(valueRange.max) - v2y(valueRange.min))
        const barSpacing = props.barSpacing ?? distBarAreaHeight * 0.04
        const distBarHeight = (distBarAreaHeight - (barCount - 1) * barSpacing) / barCount

        const boxWidth = deltaT2x(endTime - startTime) + timeUnitWidth

        const maxValue = Math.max(...props.distData)

        const topY = v2y(valueRange.max)

        const barPropsList = props.distData.map((value, index) => {
            const barWidth = (value / maxValue) * 100
            return {
                x: t2x(endTime) + timeUnitWidth / 2,
                y: topY + index * (distBarHeight + barSpacing),
                width: barWidth,
                height: distBarHeight
            }
        })
        return {
            barPropsList,
            box: {
                x: t2x(startTime) - timeUnitWidth / 2,
                y: topY,
                width: boxWidth,
                height: distBarAreaHeight
            }
        }
    }, [props.timeRange, props.distData, v2y])


    const sceneFunc = useCallback((context: Konva.Context, shape: any) => {
        if (!tradeDistProps) return;
        const boxProps = tradeDistProps.box
        const barPropsList = tradeDistProps.barPropsList

        // Draw the border rect, considering candlestick width
        context.beginPath();
        context.rect(boxProps.x, boxProps.y, boxProps.width, boxProps.height);
        context.strokeStyle = randomColor
        context.lineWidth = 2;
        context.stroke();
        context.fillStrokeShape(shape);

        // Draw each bar based on its index from top to bottom
        barPropsList.forEach((barProps) => {
            // Draw bar with shadow effect
            context.save();
            context.shadowColor = 'rgba(0, 0, 0, 0.3)';
            context.shadowBlur = 4;
            context.shadowOffsetX = 2;
            context.shadowOffsetY = 2;

            context.beginPath();
            context.rect(barProps.x, barProps.y, barProps.width, barProps.height);
            context.fillStyle = randomColor
            context.fill();
            context.restore();
            context.fillStrokeShape(shape);
        });


    }, [tradeDistProps]);


    return (
        <Shape
            sceneFunc={sceneFunc}
        />
    );
}
