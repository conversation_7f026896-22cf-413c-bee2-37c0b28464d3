import { createContext } from "react"
import { ETool, TAlertConfig, TCoord, TRange, TTick } from "../types"

export const AppContext = createContext<{
    width: number,
    symbol: string,
    timeframe: string,
    timeRange: TRange,
    timeUnit: number,
    setTimeRange: React.Dispatch<React.SetStateAction<TRange>>
    activeChart: string,
    setActiveChart: React.Dispatch<React.SetStateAction<string>>
    pointerCoord: TCoord | null,
    setPointerCoord: React.Dispatch<React.SetStateAction<TCoord | null>>,
    tick: TTick | null,
    crosshairsCoord: React.RefObject<TCoord | null>,
    activeTool: ETool,
setActiveTool: React.Dispatch<React.SetStateAction<ETool>>
    alertConfigList: TAlertConfig[]
    setAlertConfigList: React.Dispatch<React.SetStateAction<TAlertConfig[]>>
}>({
    width: 0,
    timeUnit: 0,
    symbol: '',
    timeframe: '',
    timeRange: { max: 0, min: 0 },
    setTimeRange: () => { },
    activeChart: '',
    setActiveChart: () => { },
    pointerCoord: { x: 0, y: 0 },
    setPointerCoord: () => { },
    tick: null,
    crosshairsCoord: { current: null },
    activeTool: ETool.None,
    setActiveTool: () => { },
    alertConfigList: [],
    setAlertConfigList: () => { },
})