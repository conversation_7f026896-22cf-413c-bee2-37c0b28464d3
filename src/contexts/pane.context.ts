import { createContext } from "react"
import { TDrawingConfig, TNewDrawingConfig } from "../types"

export const PaneContext = createContext<{
    selectedDrawingConfig: TDrawingConfig | null,
    setSelectedDrawingConfig: React.Dispatch<React.SetStateAction<TDrawingConfig | null>>,
    drawingConfigList: (TDrawingConfig | TNewDrawingConfig)[]
}>({
    selectedDrawingConfig: null,
    setSelectedDrawingConfig: () => { },
    drawingConfigList: []
})