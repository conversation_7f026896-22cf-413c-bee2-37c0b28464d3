import React, { createContext, useContext, useState, useMemo, useEffect } from "react";
import { TDrawingConfig, TLoader } from "../types";

type DataContextType = {
    reloadDrawingConfigList: () => Promise<void>;
    clearDrawingList: () => Promise<void>;
    drawingConfigList: TDrawingConfig[];
};

export const DataContext = createContext<DataContextType | undefined>(undefined);

export const DataProvider = (props: { children: React.ReactNode, loader: TLoader, symbol: string }) => {
    const [drawingConfigList, setDrawingConfigList] = useState<TDrawingConfig[]>([]);

    const reloadDrawingConfigList = async () => {
        const drawingConfigList = await props.loader.drawingConfig.getList({ symbol: props.symbol })
        setDrawingConfigList(drawingConfigList)
    }

    const clearDrawingList = async () => {
        await props.loader.drawingConfig.clear(props.symbol)
        await reloadDrawingConfigList()
    }

    useEffect(() => {
        reloadDrawingConfigList()
    }, [props.symbol])

    const value = useMemo(() => ({
        drawingConfigList,
        reloadDrawingConfigList,
        clearDrawingList
    }), [drawingConfigList]);

    return (
        <DataContext.Provider value={value}>
            {props.children}
        </DataContext.Provider>
    );
};

export function useDataContext() {
    const ctx = useContext(DataContext);
    if (!ctx) throw new Error("useDataContext must be used within a DataProvider");
    return ctx;
}