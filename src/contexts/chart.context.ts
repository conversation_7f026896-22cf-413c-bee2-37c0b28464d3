import { createContext } from "react"
import { TRange } from "../types"
import { ChartData } from "../storage/chartData"

export const ChartContext = createContext<{
    valueRange: TRange,
    setValueRange: React.Dispatch<React.SetStateAction<TRange>>,
    autoScale: boolean,
    setAutoScale: React.Dispatch<React.SetStateAction<boolean>>,
    chartData?: ChartData,
    height: number,
}>({
    valueRange: { max: 0, min: 0 },
    setValueRange: () => { },
    autoScale: true,
    setAutoScale: () => { },
    height: 0,
})
