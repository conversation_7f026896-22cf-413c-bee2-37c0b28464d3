import { useEffect, useMemo, useRef, useState, useCallback } from 'react'
import { Container, PaperBackground, BlueprintGrid, Footer, Watermark, globalStyles } from './styles/app.styles'
import { timeframe2timeUnit } from './common/util'
import { AppContext } from './contexts/app.context'
import { Chart } from './core/chart'
import { Toolbox } from './core/toolbox'
import { XAxis } from './core/xAxis'
import { Countdown } from './core/countdown'
import { AlertList } from './components/alertList'
import { TradingPanel } from './components/tradingPanel'
import { XAXIS_HEIGHT, YAXIS_WIDTH } from './common/constants'
import { TSolarisProps, TCoord, ETool } from './types'
import { useAppData } from './hooks/useAppData'
import { useLayout } from './hooks/useLayout'
import { useSubscription } from './hooks/useSubscription'
import { DataProvider } from './contexts/data.context'

export const App = (props: TSolarisProps) => {
  // Apply global styles to prevent blue highlight effect
  globalStyles();

  const timeUnit = useMemo(() => timeframe2timeUnit(props.timeframe), [props.timeframe]);
  const containerRef = useRef<HTMLDivElement>(null)

  // Extract layout-related state and logic
  const { width, height, getChartSizes } = useLayout(containerRef);

  // Extract time range state
  const [timeRange, setTimeRange] = useState(props.defaultTimeRange || JSON.parse(localStorage.getItem('timeRange')!) || {
    max: Date.now(),
    min: Date.now() - timeUnit * 100
  })

  // Extract chart data loading logic
  const {
    chartDataList,
    loadingChartData,
    alertConfigList,
    setAlertConfigList,
    loadInitialData,
    loadMoreData
  } = useAppData(props, timeRange, timeUnit);

  // Extract subscription logic
  const {
    tick,
    manageSubscription,
    cleanupSubscription,
    positionsRef
  } = useSubscription(props, chartDataList, timeRange, timeUnit);

  // UI state
  const [activeChart, setActiveChart] = useState('')
  const [pointerCoord, setPointerCoord] = useState<TCoord | null>(null)
  const crosshairsCoord = useRef<TCoord | null>(null)
  const [activeTool, setActiveTool] = useState(ETool.None)


  // Save timeRange to localStorage
  useEffect(() => {
    localStorage.setItem('timeRange', JSON.stringify(timeRange))
  }, [timeRange])

  // Load initial data when symbol/timeframe changes
  useEffect(() => {
    loadInitialData();

    return () => {
      cleanupSubscription();
    }
  }, [props.symbol, props.timeframe, props.isChangingSymbol, cleanupSubscription]);

  // Load more data when timeRange changes
  useEffect(() => {
    if (!loadingChartData && chartDataList.length > 0 && !props.isChangingSymbol) {
      loadMoreData(timeRange);
    }
  }, [timeRange, loadingChartData, chartDataList.length, props.isChangingSymbol]);

  // Manage subscription when data or timeRange changes
  useEffect(() => {
    if (chartDataList.length > 0) {
      manageSubscription();
    }
  }, [chartDataList, timeRange, manageSubscription]);

  const renderChartList = useCallback(() => {
    const chartList = []
    const availHeight = height - XAXIS_HEIGHT

    for (let i = 0; i < chartDataList.length; i++) {
      const chartSizes = getChartSizes(chartDataList.length, availHeight);
      chartList.push(
        <div key={chartDataList[i].name} style={{
          position: 'absolute',
          left: 0,
          top: chartSizes[i].top,
          height: chartSizes[i].height,
          width,
        }}>
          <Chart chartData={chartDataList[i]} height={chartSizes[i].height} />
        </div>
      )
    }
    return chartList
  }, [chartDataList, height, width]);

  return (
    <AppContext.Provider value={{
      alertConfigList,
      setAlertConfigList,
      width: width - YAXIS_WIDTH,
      timeUnit,
      symbol: props.symbol,
      timeframe: props.timeframe,
      timeRange,
      setTimeRange,
      activeChart,
      setActiveChart,
      pointerCoord,
      setPointerCoord,
      tick,
      crosshairsCoord,
      activeTool,
      setActiveTool,
    }}>
      <DataProvider loader={props.loader} symbol={props.symbol}>
        <Container ref={containerRef}>
          <PaperBackground />
          <Watermark src="../src/asset/solaris.png" />
          <BlueprintGrid />
          {renderChartList()}
          <Footer>
            <XAxis width={width - YAXIS_WIDTH} />
            {tick && timeUnit && (
              <Countdown
                style={{
                  width: '100%',
                  textAlign: 'center',
                  display: 'flex',
                  justifyContent: 'center'
                }}
                startTime={tick.time}
                deadline={tick.time + timeUnit}
              />
            )}
          </Footer>
          {props.enableToolbox && <Toolbox />}
          <AlertList alertConfigList={alertConfigList} />
          {props.enableTradingPanel && <TradingPanel positions={positionsRef.current} />}
        </Container>
      </DataProvider>
    </AppContext.Provider>
  )
}
