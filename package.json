{"name": "solaris", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"date-fns": "^4.1.0", "es-toolkit": "^1.37.2", "html2canvas": "^1.4.1", "konva": "^9.3.20", "qs": "^6.14.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-konva": "^19.0.3"}, "devDependencies": {"@stitches/react": "^1.2.8", "@types/qs": "^6.9.18", "@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}